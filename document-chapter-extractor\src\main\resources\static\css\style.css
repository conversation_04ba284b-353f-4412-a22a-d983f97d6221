/* Custom Styles for Document Chapter Extractor */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Drop Zone Styles */
.drop-zone {
    background-color: #fff;
    border-color: #dee2e6 !important;
    transition: var(--transition);
    cursor: pointer;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.drop-zone:hover {
    border-color: var(--primary-color) !important;
    background-color: #f8f9ff;
}

.drop-zone.drag-over {
    border-color: var(--success-color) !important;
    background-color: #f0fff4;
    transform: scale(1.02);
}

.drop-zone.drag-over i {
    color: var(--success-color) !important;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Chapter List Styles */
.chapter-item {
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    transition: var(--transition);
}

.chapter-item:hover {
    background-color: #f8f9fa;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.chapter-meta {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.chapter-preview {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-style: italic;
}

.chapter-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Progress Bar Enhancements */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* File Info Styles */
#fileInfo .alert {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .drop-zone {
        padding: 2rem 1rem !important;
        min-height: 150px;
    }
    
    .drop-zone h4 {
        font-size: 1.1rem;
    }
    
    .chapter-item {
        padding: 1rem;
    }
    
    .chapter-actions {
        justify-content: center;
    }
    
    .btn-lg {
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success State */
.success-state {
    border-left: 4px solid var(--success-color);
}

/* Error State */
.error-state {
    border-left: 4px solid var(--danger-color);
}

/* Custom Scrollbar */
.chapter-preview {
    max-height: 100px;
    overflow-y: auto;
}

.chapter-preview::-webkit-scrollbar {
    width: 4px;
}

.chapter-preview::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.chapter-preview::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.chapter-preview::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
