// Document Chapter Extractor - Frontend JavaScript

class DocumentProcessor {
    constructor() {
        this.currentFile = null;
        this.sessionId = null;
        this.chapters = [];
        
        this.initializeElements();
        this.setupEventListeners();
    }
    
    initializeElements() {
        // File upload elements
        this.dropZone = document.getElementById('dropZone');
        this.fileInput = document.getElementById('fileInput');
        this.browseBtn = document.getElementById('browseBtn');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.removeFileBtn = document.getElementById('removeFile');
        
        // File info elements
        this.fileInfo = document.getElementById('fileInfo');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        
        // Progress elements
        this.progressContainer = document.getElementById('progressContainer');
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        
        // Results elements
        this.resultsSection = document.getElementById('resultsSection');
        this.resultFileName = document.getElementById('resultFileName');
        this.resultFileType = document.getElementById('resultFileType');
        this.resultFileSize = document.getElementById('resultFileSize');
        this.totalChapters = document.getElementById('totalChapters');
        this.totalWords = document.getElementById('totalWords');
        this.chaptersList = document.getElementById('chaptersList');
        this.downloadAllBtn = document.getElementById('downloadAllBtn');
        
        // Error elements
        this.errorSection = document.getElementById('errorSection');
        this.errorMessage = document.getElementById('errorMessage');
        
        // Loading modal
        this.loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        this.loadingText = document.getElementById('loadingText');
    }
    
    setupEventListeners() {
        // File input events
        this.browseBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        this.uploadBtn.addEventListener('click', () => this.uploadFile());
        this.removeFileBtn.addEventListener('click', () => this.removeFile());
        
        // Drag and drop events
        this.dropZone.addEventListener('click', () => this.fileInput.click());
        this.dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.dropZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.dropZone.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Download all button
        this.downloadAllBtn.addEventListener('click', () => this.downloadAllChapters());
        
        // Prevent default drag behaviors on document
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());
    }
    
    handleDragOver(e) {
        e.preventDefault();
        this.dropZone.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        if (!this.dropZone.contains(e.relatedTarget)) {
            this.dropZone.classList.remove('drag-over');
        }
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelect(files[0]);
        }
    }
    
    handleFileSelect(file) {
        if (!file) return;
        
        // Validate file type
        const allowedTypes = [
            'application/pdf',
            'application/epub+zip',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/vnd.oasis.opendocument.text',
            'application/rtf'
        ];
        
        if (!allowedTypes.includes(file.type) && !this.isValidFileExtension(file.name)) {
            this.showError('Unsupported file type. Please select a PDF, EPUB, DOC, DOCX, TXT, ODT, or RTF file.');
            return;
        }
        
        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            this.showError('File size exceeds 50MB limit. Please select a smaller file.');
            return;
        }
        
        this.currentFile = file;
        this.displayFileInfo();
        this.uploadBtn.disabled = false;
        this.hideError();
    }
    
    isValidFileExtension(fileName) {
        const validExtensions = ['.pdf', '.epub', '.doc', '.docx', '.txt', '.odt', '.rtf'];
        const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return validExtensions.includes(extension);
    }
    
    displayFileInfo() {
        this.fileName.textContent = this.currentFile.name;
        this.fileSize.textContent = this.formatFileSize(this.currentFile.size);
        this.fileInfo.classList.remove('d-none');
        this.fileInfo.classList.add('fade-in');
    }
    
    removeFile() {
        this.currentFile = null;
        this.fileInput.value = '';
        this.fileInfo.classList.add('d-none');
        this.uploadBtn.disabled = true;
        this.hideResults();
        this.hideError();
    }
    
    async uploadFile() {
        if (!this.currentFile) return;
        
        this.showProgress();
        this.loadingModal.show();
        this.loadingText.textContent = 'Processing document...';
        
        const formData = new FormData();
        formData.append('file', this.currentFile);
        
        try {
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.sessionId = result.sessionId;
                this.chapters = result.chapters;
                this.displayResults(result);
                this.hideError();
            } else {
                this.showError(result.message || 'Failed to process document');
                this.hideResults();
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Network error occurred. Please try again.');
            this.hideResults();
        } finally {
            this.hideProgress();
            this.loadingModal.hide();
        }
    }
    
    displayResults(result) {
        // Update document info
        this.resultFileName.textContent = result.fileName;
        this.resultFileType.textContent = this.getFileTypeDisplay(result.fileType);
        this.resultFileSize.textContent = this.formatFileSize(result.fileSize);
        this.totalChapters.textContent = result.totalChapters;
        this.totalWords.textContent = result.totalWords.toLocaleString();
        
        // Display chapters
        this.displayChapters(result.chapters);
        
        // Show results section
        this.resultsSection.classList.remove('d-none');
        this.resultsSection.classList.add('fade-in');
        
        // Scroll to results
        this.resultsSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    displayChapters(chapters) {
        this.chaptersList.innerHTML = '';
        
        chapters.forEach((chapter, index) => {
            const chapterElement = this.createChapterElement(chapter, index);
            this.chaptersList.appendChild(chapterElement);
        });
    }
    
    createChapterElement(chapter, index) {
        const div = document.createElement('div');
        div.className = 'chapter-item';
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h5 class="chapter-title">${this.escapeHtml(chapter.title)}</h5>
                    <div class="chapter-meta">
                        <i class="fas fa-align-left me-1"></i>
                        ${chapter.wordCount.toLocaleString()} words
                        <span class="ms-3">
                            <i class="fas fa-hashtag me-1"></i>
                            Chapter ${chapter.chapterNumber}
                        </span>
                    </div>
                    <div class="chapter-preview">
                        ${this.escapeHtml(chapter.preview)}
                    </div>
                </div>
                <div class="chapter-actions ms-3">
                    <button class="btn btn-primary btn-sm download-chapter-btn" 
                            data-chapter-id="${chapter.id}" 
                            data-chapter-title="${this.escapeHtml(chapter.title)}">
                        <i class="fas fa-download me-1"></i>
                        Download
                    </button>
                </div>
            </div>
        `;
        
        // Add download event listener
        const downloadBtn = div.querySelector('.download-chapter-btn');
        downloadBtn.addEventListener('click', () => this.downloadChapter(chapter.id, chapter.title));
        
        return div;
    }
    
    async downloadChapter(chapterId, chapterTitle) {
        if (!this.sessionId) return;
        
        try {
            const response = await fetch(`/api/download/${this.sessionId}/${chapterId}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')
                    ?.split('filename=')[1]?.replace(/"/g, '') || 'chapter.txt';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showSuccess(`Downloaded: ${chapterTitle}`);
            } else {
                this.showError('Failed to download chapter');
            }
        } catch (error) {
            console.error('Download error:', error);
            this.showError('Network error occurred during download');
        }
    }
    
    async downloadAllChapters() {
        if (!this.sessionId) return;
        
        try {
            const response = await fetch(`/api/download-zip/${this.sessionId}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'chapters.zip';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showSuccess('Downloaded all chapters as ZIP file');
            } else {
                this.showError('Failed to download chapters');
            }
        } catch (error) {
            console.error('Download error:', error);
            this.showError('Network error occurred during download');
        }
    }
    
    showProgress() {
        this.progressContainer.classList.remove('d-none');
        this.progressBar.style.width = '100%';
        this.progressText.textContent = 'Processing...';
    }
    
    hideProgress() {
        this.progressContainer.classList.add('d-none');
        this.progressBar.style.width = '0%';
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorSection.classList.remove('d-none');
        this.errorSection.classList.add('fade-in');
        this.errorSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    hideError() {
        this.errorSection.classList.add('d-none');
    }
    
    hideResults() {
        this.resultsSection.classList.add('d-none');
    }
    
    showSuccess(message) {
        // Create temporary success alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    getFileTypeDisplay(mimeType) {
        const typeMap = {
            'application/pdf': 'PDF Document',
            'application/epub+zip': 'EPUB eBook',
            'application/msword': 'Word Document (DOC)',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document (DOCX)',
            'text/plain': 'Text File',
            'application/vnd.oasis.opendocument.text': 'OpenDocument Text',
            'application/rtf': 'Rich Text Format'
        };
        return typeMap[mimeType] || mimeType;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentProcessor();
});
