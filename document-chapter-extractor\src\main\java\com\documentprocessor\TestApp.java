package com.documentprocessor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class TestApp {
    
    @GetMapping("/")
    public String home() {
        return "Document Chapter Extractor is running!";
    }
    
    @GetMapping("/test")
    public String test() {
        return "Test endpoint working!";
    }
    
    public static void main(String[] args) {
        System.out.println("Starting Document Chapter Extractor...");
        SpringApplication.run(TestApp.class, args);
        System.out.println("Application started successfully!");
    }
}
