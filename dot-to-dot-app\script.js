/**
 * Dot-to-Dot Creator - Interactive Image Editor
 * Complete web-based application for creating dot-to-dot drawings
 */

class DotToDotCreator {
    constructor() {
        // Core properties
        this.currentImage = null;
        this.dots = [];
        this.isDragging = false;
        this.dragIndex = -1;
        this.dragOffset = { x: 0, y: 0 };
        this.zoom = 1;
        this.panOffset = { x: 0, y: 0 };
        
        // Settings
        this.settings = {
            dotCount: 50,
            edgeSensitivity: 50,
            showConnections: true,
            showBackground: true
        };
        
        // History for undo/redo
        this.history = [];
        this.historyIndex = -1;
        
        // Canvas elements
        this.backgroundCanvas = null;
        this.dotsCanvas = null;
        this.backgroundCtx = null;
        this.dotsCtx = null;
        
        // DOM elements
        this.elements = {};
        
        this.init();
    }
    
    init() {
        try {
            this.setupElements();
            this.setupCanvas();
            this.setupEventListeners();
            this.updateUI();
            console.log('DotToDotCreator initialized successfully');
        } catch (error) {
            console.error('Error initializing DotToDotCreator:', error);
        }
    }
    
    setupElements() {
        // Get all required DOM elements
        const elementIds = [
            'uploadArea', 'fileInput', 'uploadPreview', 'previewImage', 'removeImage',
            'dotCount', 'dotCountValue', 'edgeSensitivity', 'edgeSensitivityValue',
            'showConnections', 'showBackground', 'generateBtn', 'evenSpaceBtn',
            'autoRenumberBtn', 'clearAllBtn', 'exportPNG', 'exportJPEG', 'exportSVG',
            'undoBtn', 'redoBtn', 'canvasInfo', 'zoomOut', 'zoomIn', 'resetZoom',
            'zoomLevel', 'canvasContainer', 'backgroundCanvas', 'dotsCanvas',
            'canvasPlaceholder', 'statusMessage', 'progressContainer', 'progressFill',
            'progressText', 'loadingOverlay', 'darkModeToggle'
        ];
        
        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
            if (!this.elements[id]) {
                console.error(`Element not found: ${id}`);
            }
        });
        
        // Set canvas references
        this.backgroundCanvas = this.elements.backgroundCanvas;
        this.dotsCanvas = this.elements.dotsCanvas;
        this.backgroundCtx = this.backgroundCanvas.getContext('2d');
        this.dotsCtx = this.dotsCanvas.getContext('2d');
    }
    
    setupCanvas() {
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        const container = this.elements.canvasContainer;
        const rect = container.getBoundingClientRect();
        
        // Set canvas size
        this.backgroundCanvas.width = rect.width;
        this.backgroundCanvas.height = rect.height;
        this.dotsCanvas.width = rect.width;
        this.dotsCanvas.height = rect.height;
        
        // Redraw if image exists
        if (this.currentImage) {
            this.drawBackground();
            this.drawDots();
        }
    }
    
    setupEventListeners() {
        // File upload
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('dragover');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', () => {
            this.elements.uploadArea.classList.remove('dragover');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });
        
        // Remove image
        this.elements.removeImage.addEventListener('click', () => {
            this.removeImage();
        });
        
        // Settings
        this.elements.dotCount.addEventListener('input', (e) => {
            this.settings.dotCount = parseInt(e.target.value);
            this.elements.dotCountValue.textContent = e.target.value;
        });
        
        this.elements.edgeSensitivity.addEventListener('input', (e) => {
            this.settings.edgeSensitivity = parseInt(e.target.value);
            this.elements.edgeSensitivityValue.textContent = e.target.value;
        });
        
        this.elements.showConnections.addEventListener('change', (e) => {
            this.settings.showConnections = e.target.checked;
            this.drawDots();
        });
        
        this.elements.showBackground.addEventListener('change', (e) => {
            this.settings.showBackground = e.target.checked;
            this.drawBackground();
        });
        
        // Action buttons
        this.elements.generateBtn.addEventListener('click', () => {
            this.generateDotToDot();
        });
        
        this.elements.evenSpaceBtn.addEventListener('click', () => {
            this.evenSpaceDots();
        });
        
        this.elements.autoRenumberBtn.addEventListener('click', () => {
            this.autoRenumber();
        });
        
        this.elements.clearAllBtn.addEventListener('click', () => {
            this.clearAll();
        });
        
        // Export buttons
        this.elements.exportPNG.addEventListener('click', () => {
            this.exportImage('png');
        });
        
        this.elements.exportJPEG.addEventListener('click', () => {
            this.exportImage('jpeg');
        });
        
        this.elements.exportSVG.addEventListener('click', () => {
            this.exportSVG();
        });
        
        // History buttons
        this.elements.undoBtn.addEventListener('click', () => {
            this.undo();
        });
        
        this.elements.redoBtn.addEventListener('click', () => {
            this.redo();
        });
        
        // Zoom controls
        this.elements.zoomIn.addEventListener('click', () => {
            this.zoomIn();
        });
        
        this.elements.zoomOut.addEventListener('click', () => {
            this.zoomOut();
        });
        
        this.elements.resetZoom.addEventListener('click', () => {
            this.resetZoom();
        });
        
        // Canvas mouse events
        this.dotsCanvas.addEventListener('mousedown', (e) => {
            this.handleMouseDown(e);
        });
        
        this.dotsCanvas.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });
        
        this.dotsCanvas.addEventListener('mouseup', (e) => {
            this.handleMouseUp(e);
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
        
        // Dark mode toggle
        this.elements.darkModeToggle.addEventListener('click', () => {
            this.toggleDarkMode();
        });
    }
    
    // File handling methods
    handleFileSelect(files) {
        if (files && files.length > 0) {
            this.loadImage(files[0]);
        }
    }
    
    loadImage(file) {
        if (!file.type.startsWith('image/')) {
            this.showStatus('Please select a valid image file', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.currentImage = img;
                this.setupImageDisplay();
                this.showStatus('Image loaded successfully', 'success');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    setupImageDisplay() {
        // Show preview
        this.elements.previewImage.src = this.currentImage.src;
        this.elements.uploadPreview.style.display = 'block';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'none';
        
        // Hide placeholder
        this.elements.canvasPlaceholder.style.display = 'none';
        
        // Enable buttons
        this.elements.generateBtn.disabled = false;
        
        // Reset zoom and draw
        this.resetZoom();
        this.drawBackground();
        
        // Update info
        this.elements.canvasInfo.textContent = `${this.currentImage.width} × ${this.currentImage.height}px`;
    }
    
    removeImage() {
        this.currentImage = null;
        this.dots = [];
        this.clearHistory();
        
        // Reset UI
        this.elements.uploadPreview.style.display = 'none';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'block';
        this.elements.canvasPlaceholder.style.display = 'block';
        
        // Disable buttons
        this.disableAllButtons();
        
        // Clear canvas
        this.backgroundCtx.clearRect(0, 0, this.backgroundCanvas.width, this.backgroundCanvas.height);
        this.dotsCtx.clearRect(0, 0, this.dotsCanvas.width, this.dotsCanvas.height);
        
        this.showStatus('Image removed', 'info');
        this.elements.canvasInfo.textContent = 'Ready to upload image';
    }

    // Canvas drawing methods
    drawBackground() {
        this.backgroundCtx.clearRect(0, 0, this.backgroundCanvas.width, this.backgroundCanvas.height);

        if (!this.currentImage || !this.settings.showBackground) {
            return;
        }

        const canvas = this.backgroundCanvas;
        const img = this.currentImage;

        // Calculate scaled dimensions
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        // Center the image
        const x = (canvas.width - scaledWidth) / 2 + this.panOffset.x;
        const y = (canvas.height - scaledHeight) / 2 + this.panOffset.y;

        // Draw with reduced opacity for background
        this.backgroundCtx.globalAlpha = 0.3;
        this.backgroundCtx.drawImage(img, x, y, scaledWidth, scaledHeight);
        this.backgroundCtx.globalAlpha = 1.0;
    }

    drawDots() {
        this.dotsCtx.clearRect(0, 0, this.dotsCanvas.width, this.dotsCanvas.height);

        if (this.dots.length === 0) {
            return;
        }

        const canvas = this.dotsCanvas;
        const ctx = this.dotsCtx;

        // Draw connecting lines if enabled
        if (this.settings.showConnections && this.dots.length > 1) {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();

            this.dots.forEach((dot, i) => {
                const canvasDot = this.transformDotToCanvas(dot);
                if (i === 0) {
                    ctx.moveTo(canvasDot.x, canvasDot.y);
                } else {
                    ctx.lineTo(canvasDot.x, canvasDot.y);
                }
            });
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // Draw dots and numbers
        this.dots.forEach((dot, index) => {
            const canvasDot = this.transformDotToCanvas(dot);

            // Draw perfect circular dot
            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.arc(canvasDot.x, canvasDot.y, 5, 0, 2 * Math.PI);
            ctx.fill();

            // Add white border to make dot more visible
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Draw number with circular background
            const text = (index + 1).toString();
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const metrics = ctx.measureText(text);
            const textRadius = Math.max(8, metrics.width / 2 + 3);

            // White circular background for number
            ctx.fillStyle = '#fff';
            ctx.beginPath();
            ctx.arc(canvasDot.x, canvasDot.y - 18, textRadius, 0, 2 * Math.PI);
            ctx.fill();

            // Black border around number background
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Black text
            ctx.fillStyle = '#000';
            ctx.fillText(text, canvasDot.x, canvasDot.y - 18);
        });
    }

    transformDotToCanvas(dot) {
        if (!this.currentImage) return dot;

        const canvas = this.dotsCanvas;
        const img = this.currentImage;

        // Calculate scale and position
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        const offsetX = (canvas.width - scaledWidth) / 2 + this.panOffset.x;
        const offsetY = (canvas.height - scaledHeight) / 2 + this.panOffset.y;

        return {
            x: dot.x * scale + offsetX,
            y: dot.y * scale + offsetY
        };
    }

    transformCanvasToImage(canvasPoint) {
        if (!this.currentImage) return canvasPoint;

        const canvas = this.dotsCanvas;
        const img = this.currentImage;

        // Calculate scale and position
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        const offsetX = (canvas.width - scaledWidth) / 2 + this.panOffset.x;
        const offsetY = (canvas.height - scaledHeight) / 2 + this.panOffset.y;

        return {
            x: (canvasPoint.x - offsetX) / scale,
            y: (canvasPoint.y - offsetY) / scale
        };
    }

    // Mouse interaction methods
    handleMouseDown(event) {
        const rect = this.dotsCanvas.getBoundingClientRect();
        const canvasPoint = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };

        // Check if clicking on existing dot
        for (let i = 0; i < this.dots.length; i++) {
            const canvasDot = this.transformDotToCanvas(this.dots[i]);
            const distance = Math.sqrt(
                Math.pow(canvasPoint.x - canvasDot.x, 2) +
                Math.pow(canvasPoint.y - canvasDot.y, 2)
            );

            if (distance <= 15) { // 15px click tolerance
                this.isDragging = true;
                this.dragIndex = i;
                this.dragOffset = {
                    x: canvasPoint.x - canvasDot.x,
                    y: canvasPoint.y - canvasDot.y
                };
                this.dotsCanvas.style.cursor = 'grabbing';
                break;
            }
        }
    }

    handleMouseMove(event) {
        const rect = this.dotsCanvas.getBoundingClientRect();
        const canvasPoint = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };

        if (this.isDragging && this.dragIndex >= 0) {
            // Update dot position
            const adjustedPoint = {
                x: canvasPoint.x - this.dragOffset.x,
                y: canvasPoint.y - this.dragOffset.y
            };

            const newDotPos = this.transformCanvasToImage(adjustedPoint);

            // Constrain to image bounds
            if (this.currentImage) {
                newDotPos.x = Math.max(0, Math.min(this.currentImage.width, newDotPos.x));
                newDotPos.y = Math.max(0, Math.min(this.currentImage.height, newDotPos.y));
            }

            this.dots[this.dragIndex] = newDotPos;
            this.drawDots();
        } else {
            // Update cursor based on hover
            let overDot = false;
            for (let i = 0; i < this.dots.length; i++) {
                const canvasDot = this.transformDotToCanvas(this.dots[i]);
                const distance = Math.sqrt(
                    Math.pow(canvasPoint.x - canvasDot.x, 2) +
                    Math.pow(canvasPoint.y - canvasDot.y, 2)
                );

                if (distance <= 15) {
                    overDot = true;
                    break;
                }
            }

            this.dotsCanvas.style.cursor = overDot ? 'grab' : 'default';
        }
    }

    handleMouseUp(event) {
        if (this.isDragging) {
            this.saveState();
            this.isDragging = false;
            this.dragIndex = -1;
            this.dotsCanvas.style.cursor = 'default';
        }
    }

    // Keyboard shortcuts
    handleKeyDown(event) {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key.toLowerCase()) {
                case 'z':
                    event.preventDefault();
                    if (event.shiftKey) {
                        this.redo();
                    } else {
                        this.undo();
                    }
                    break;
                case 'y':
                    event.preventDefault();
                    this.redo();
                    break;
            }
        }

        // Delete key to remove selected dot
        if (event.key === 'Delete' && this.dragIndex >= 0) {
            this.deleteDot(this.dragIndex);
        }
    }

    // Zoom and pan methods
    zoomIn() {
        this.zoom = Math.min(this.zoom * 1.2, 5);
        this.updateZoomDisplay();
        this.redraw();
    }

    zoomOut() {
        this.zoom = Math.max(this.zoom / 1.2, 0.1);
        this.updateZoomDisplay();
        this.redraw();
    }

    resetZoom() {
        this.zoom = 1;
        this.panOffset = { x: 0, y: 0 };
        this.updateZoomDisplay();
        this.redraw();
    }

    updateZoomDisplay() {
        this.elements.zoomLevel.textContent = `${Math.round(this.zoom * 100)}%`;
    }

    redraw() {
        this.drawBackground();
        this.drawDots();
    }

    // History management
    saveState() {
        // Remove any future history if we're not at the end
        if (this.historyIndex < this.history.length - 1) {
            this.history = this.history.slice(0, this.historyIndex + 1);
        }

        // Add new state
        this.history.push(JSON.parse(JSON.stringify(this.dots)));

        // Limit history size
        if (this.history.length > 50) {
            this.history.shift();
        } else {
            this.historyIndex++;
        }

        this.updateHistoryButtons();
    }

    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.dots = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.drawDots();
            this.updateHistoryButtons();
        }
    }

    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.dots = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.drawDots();
            this.updateHistoryButtons();
        }
    }

    updateHistoryButtons() {
        this.elements.undoBtn.disabled = this.historyIndex <= 0;
        this.elements.redoBtn.disabled = this.historyIndex >= this.history.length - 1;
    }

    clearHistory() {
        this.history = [];
        this.historyIndex = -1;
        this.updateHistoryButtons();
    }

    // Dot manipulation methods
    deleteDot(index) {
        if (index >= 0 && index < this.dots.length) {
            this.dots.splice(index, 1);
            this.drawDots();
            this.saveState();

            if (this.dots.length === 0) {
                this.disableEditingButtons();
                this.disableExportButtons();
            }
        }
    }

    autoRenumber() {
        // Dots are already in order by array index
        this.drawDots();
        this.saveState();
        this.showStatus('Dots renumbered', 'success');
    }

    evenSpaceDots() {
        if (this.dots.length < 2) return;

        // Calculate total path length
        let totalLength = 0;
        for (let i = 0; i < this.dots.length - 1; i++) {
            const dx = this.dots[i + 1].x - this.dots[i].x;
            const dy = this.dots[i + 1].y - this.dots[i].y;
            totalLength += Math.sqrt(dx * dx + dy * dy);
        }

        // Redistribute dots evenly along the path
        const segmentLength = totalLength / (this.dots.length - 1);
        const newDots = [this.dots[0]]; // Keep first dot

        let currentLength = 0;
        let currentSegment = 0;

        for (let i = 1; i < this.dots.length - 1; i++) {
            const targetLength = i * segmentLength;

            // Find the segment where this dot should be placed
            while (currentSegment < this.dots.length - 1) {
                const dx = this.dots[currentSegment + 1].x - this.dots[currentSegment].x;
                const dy = this.dots[currentSegment + 1].y - this.dots[currentSegment].y;
                const segLen = Math.sqrt(dx * dx + dy * dy);

                if (currentLength + segLen >= targetLength) {
                    // Interpolate position within this segment
                    const ratio = (targetLength - currentLength) / segLen;
                    const newDot = {
                        x: this.dots[currentSegment].x + dx * ratio,
                        y: this.dots[currentSegment].y + dy * ratio
                    };
                    newDots.push(newDot);
                    break;
                }

                currentLength += segLen;
                currentSegment++;
            }
        }

        newDots.push(this.dots[this.dots.length - 1]); // Keep last dot
        this.dots = newDots;
        this.drawDots();
        this.showStatus('Dots evenly spaced', 'success');
    }

    clearAll() {
        if (this.dots.length > 0) {
            this.dots = [];
            this.clearHistory();
            this.drawDots();
            this.disableEditingButtons();
            this.disableExportButtons();
            this.showStatus('All dots cleared', 'info');
        }
    }

    // Backend integration
    async generateDotToDot() {
        if (!this.currentImage) return;

        this.showLoading(true);
        this.showStatus('Generating dot-to-dot...', 'info');

        try {
            // Convert image to base64
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = this.currentImage.width;
            canvas.height = this.currentImage.height;
            ctx.drawImage(this.currentImage, 0, 0);
            const imageData = canvas.toDataURL('image/png');

            // Send to backend
            const response = await fetch('/api/generate-dots', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image: imageData,
                    dotCount: this.settings.dotCount,
                    edgeSensitivity: this.settings.edgeSensitivity
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.dots) {
                this.dots = result.dots;
                this.saveState();
                this.drawDots();
                this.enableEditingButtons();
                this.enableExportButtons();
                this.showStatus(`Generated ${this.dots.length} dots`, 'success');
            } else {
                throw new Error(result.error || 'Failed to generate dots');
            }

        } catch (error) {
            console.error('Error generating dot-to-dot:', error);
            this.showStatus('Failed to generate dot-to-dot. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Export methods
    exportImage(format) {
        if (this.dots.length === 0) {
            this.showStatus('No dots to export', 'error');
            return;
        }

        // Create export canvas
        const exportCanvas = document.createElement('canvas');
        const exportCtx = exportCanvas.getContext('2d');

        // Set canvas size to original image size or default
        const width = this.currentImage ? this.currentImage.width : 800;
        const height = this.currentImage ? this.currentImage.height : 600;
        exportCanvas.width = width;
        exportCanvas.height = height;

        // White background
        exportCtx.fillStyle = '#ffffff';
        exportCtx.fillRect(0, 0, width, height);

        // Draw dots and numbers (no connecting lines for clean export)
        this.dots.forEach((dot, index) => {
            // Draw perfect circular dot
            exportCtx.fillStyle = '#000000';
            exportCtx.beginPath();
            exportCtx.arc(dot.x, dot.y, 4, 0, 2 * Math.PI);
            exportCtx.fill();

            // Draw number with circular background
            const text = (index + 1).toString();
            exportCtx.font = 'bold 12px Arial';
            exportCtx.textAlign = 'center';
            exportCtx.textBaseline = 'middle';

            const metrics = exportCtx.measureText(text);
            const textRadius = Math.max(8, metrics.width / 2 + 3);

            // White circular background for number
            exportCtx.fillStyle = '#ffffff';
            exportCtx.beginPath();
            exportCtx.arc(dot.x, dot.y - 18, textRadius, 0, 2 * Math.PI);
            exportCtx.fill();

            // Black border around number background
            exportCtx.strokeStyle = '#000000';
            exportCtx.lineWidth = 1;
            exportCtx.stroke();

            // Black text
            exportCtx.fillStyle = '#000000';
            exportCtx.fillText(text, dot.x, dot.y - 18);
        });

        // Download
        const link = document.createElement('a');
        link.download = `dot-to-dot.${format}`;
        link.href = exportCanvas.toDataURL(`image/${format}`);
        link.click();

        this.showStatus(`Exported as ${format.toUpperCase()}`, 'success');
    }

    exportSVG() {
        if (this.dots.length === 0) {
            this.showStatus('No dots to export', 'error');
            return;
        }

        const width = this.currentImage ? this.currentImage.width : 800;
        const height = this.currentImage ? this.currentImage.height : 600;

        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
        svg += `<rect width="100%" height="100%" fill="white"/>`;

        // Add dots and numbers
        this.dots.forEach((dot, index) => {
            const text = (index + 1).toString();
            const textRadius = Math.max(8, text.length * 3 + 3);

            // Perfect circular dot
            svg += `<circle cx="${dot.x}" cy="${dot.y}" r="4" fill="black"/>`;

            // Circular background for number
            svg += `<circle cx="${dot.x}" cy="${dot.y - 18}" r="${textRadius}" fill="white" stroke="black" stroke-width="1"/>`;

            // Number text
            svg += `<text x="${dot.x}" y="${dot.y - 18}" text-anchor="middle" dominant-baseline="central" font-family="Arial" font-size="12" font-weight="bold" fill="black">${text}</text>`;
        });

        svg += '</svg>';

        // Download
        const blob = new Blob([svg], { type: 'image/svg+xml' });
        const link = document.createElement('a');
        link.download = 'dot-to-dot.svg';
        link.href = URL.createObjectURL(blob);
        link.click();

        this.showStatus('Exported as SVG', 'success');
    }

    // UI helper methods
    updateUI() {
        this.disableAllButtons();
        this.updateHistoryButtons();
        this.updateZoomDisplay();
    }

    disableAllButtons() {
        this.elements.generateBtn.disabled = true;
        this.disableEditingButtons();
        this.disableExportButtons();
    }

    disableEditingButtons() {
        this.elements.evenSpaceBtn.disabled = true;
        this.elements.autoRenumberBtn.disabled = true;
        this.elements.clearAllBtn.disabled = true;
    }

    enableEditingButtons() {
        this.elements.evenSpaceBtn.disabled = false;
        this.elements.autoRenumberBtn.disabled = false;
        this.elements.clearAllBtn.disabled = false;
    }

    disableExportButtons() {
        this.elements.exportPNG.disabled = true;
        this.elements.exportJPEG.disabled = true;
        this.elements.exportSVG.disabled = true;
    }

    enableExportButtons() {
        this.elements.exportPNG.disabled = false;
        this.elements.exportJPEG.disabled = false;
        this.elements.exportSVG.disabled = false;
    }

    showStatus(message, type = 'info') {
        this.elements.statusMessage.textContent = message;
        this.elements.statusMessage.className = `status-message ${type}`;

        // Auto-clear after 3 seconds
        setTimeout(() => {
            this.elements.statusMessage.textContent = '';
            this.elements.statusMessage.className = 'status-message';
        }, 3000);
    }

    showLoading(show) {
        this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    toggleDarkMode() {
        const body = document.body;
        const isDark = body.getAttribute('data-theme') === 'dark';

        if (isDark) {
            body.removeAttribute('data-theme');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            body.setAttribute('data-theme', 'dark');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DotToDotCreator();
});
