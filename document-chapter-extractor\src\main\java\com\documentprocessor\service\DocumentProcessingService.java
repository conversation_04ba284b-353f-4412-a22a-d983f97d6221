package com.documentprocessor.service;

import com.documentprocessor.model.Chapter;
import com.documentprocessor.model.DocumentProcessingResult;
import org.apache.tika.Tika;
import org.apache.tika.config.TikaConfig;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.sax.BodyContentHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class DocumentProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessingService.class);
    
    // In-memory storage for processed documents (session-based)
    private final Map<String, List<Chapter>> sessionChapters = new ConcurrentHashMap<>();
    
    // Supported file types
    private static final Set<String> SUPPORTED_TYPES = Set.of(
        "application/pdf",
        "application/epub+zip",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/plain",
        "application/vnd.oasis.opendocument.text",
        "application/rtf",
        "application/x-mobipocket-ebook",
        "application/vnd.amazon.ebook"
    );
    
    // Chapter detection patterns
    private static final List<Pattern> CHAPTER_PATTERNS = Arrays.asList(
        Pattern.compile("(?i)^\\s*chapter\\s+\\d+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*chapter\\s+[ivxlcdm]+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*chapter\\s+[a-z]+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*\\d+\\.\\s+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*part\\s+\\d+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*section\\s+\\d+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*[ivxlcdm]+\\.\\s+.*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*prologue\\s*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*epilogue\\s*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*introduction\\s*$", Pattern.MULTILINE),
        Pattern.compile("(?i)^\\s*conclusion\\s*$", Pattern.MULTILINE)
    );
    
    public DocumentProcessingResult processDocument(MultipartFile file) {
        try {
            logger.info("Processing document: {}, size: {} bytes", file.getOriginalFilename(), file.getSize());

            // Validate file
            if (file.isEmpty()) {
                return DocumentProcessingResult.error("File is empty");
            }

            String contentType = detectContentType(file);
            logger.info("Detected content type: {}", contentType);

            if (!SUPPORTED_TYPES.contains(contentType)) {
                return DocumentProcessingResult.error("Unsupported file type: " + contentType + ". Supported types: PDF, EPUB, DOC, DOCX, TXT, ODT, RTF");
            }

            // Extract text content
            String textContent = extractTextContent(file);
            if (textContent == null || textContent.trim().isEmpty()) {
                return DocumentProcessingResult.error("Could not extract text content from the file. The file may be corrupted or contain only images.");
            }

            logger.info("Extracted text content: {} characters", textContent.length());

            // Detect and extract chapters
            List<Chapter> chapters = detectChapters(textContent);
            if (chapters.isEmpty()) {
                // If no chapters detected, create a single chapter with all content
                chapters.add(new Chapter(
                    UUID.randomUUID().toString(),
                    "Full Document",
                    textContent,
                    1
                ));
                logger.info("No chapters detected, created single chapter with full content");
            } else {
                logger.info("Detected {} chapters", chapters.size());
            }

            // Generate session ID and store chapters
            String sessionId = UUID.randomUUID().toString();
            sessionChapters.put(sessionId, chapters);

            logger.info("Successfully processed document: {} chapters extracted, session: {}", chapters.size(), sessionId);

            return DocumentProcessingResult.success(
                sessionId,
                file.getOriginalFilename(),
                contentType,
                file.getSize(),
                chapters
            );

        } catch (Exception e) {
            logger.error("Error processing document: {}", e.getMessage(), e);
            return DocumentProcessingResult.error("Error processing document: " + e.getMessage());
        }
    }
    
    private String detectContentType(MultipartFile file) throws IOException {
        try {
            Tika tika = new Tika();
            try (InputStream inputStream = file.getInputStream()) {
                String contentType = tika.detect(inputStream, file.getOriginalFilename());
                logger.info("Tika detected content type: {} for file: {}", contentType, file.getOriginalFilename());
                return contentType;
            }
        } catch (Exception e) {
            logger.error("Error detecting content type for file: {}", file.getOriginalFilename(), e);
            // Fallback to file extension based detection
            String filename = file.getOriginalFilename();
            if (filename != null) {
                String extension = filename.toLowerCase();
                if (extension.endsWith(".pdf")) return "application/pdf";
                if (extension.endsWith(".docx")) return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                if (extension.endsWith(".doc")) return "application/msword";
                if (extension.endsWith(".txt")) return "text/plain";
                if (extension.endsWith(".epub")) return "application/epub+zip";
                if (extension.endsWith(".odt")) return "application/vnd.oasis.opendocument.text";
                if (extension.endsWith(".rtf")) return "application/rtf";
            }
            throw new IOException("Could not detect content type: " + e.getMessage(), e);
        }
    }
    
    private String extractTextContent(MultipartFile file) throws IOException, TikaException, SAXException {
        try {
            // Create a simplified Tika configuration that doesn't use external parsers
            TikaConfig config = TikaConfig.getDefaultConfig();
            AutoDetectParser parser = new AutoDetectParser(config);

            // Disable external parsers to avoid initialization issues
            ParseContext context = new ParseContext();
            context.set(Parser.class, parser);

            BodyContentHandler handler = new BodyContentHandler(-1); // No limit on content length
            Metadata metadata = new Metadata();

            logger.info("Starting text extraction with Tika...");

            try (InputStream inputStream = file.getInputStream()) {
                parser.parse(inputStream, handler, metadata, context);
                String extractedText = handler.toString();

                logger.info("Text extraction completed. Extracted {} characters", extractedText.length());

                // Basic validation
                if (extractedText == null || extractedText.trim().isEmpty()) {
                    throw new RuntimeException("No text content could be extracted from the file");
                }

                return extractedText;
            }
        } catch (Exception e) {
            logger.error("Error extracting text from file: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to extract text from file: " + e.getMessage(), e);
        }
    }
    
    private List<Chapter> detectChapters(String content) {
        try {
            logger.info("Starting chapter detection for content with {} characters", content.length());
            List<Chapter> chapters = new ArrayList<>();
            List<ChapterMatch> matches = new ArrayList<>();

            // Find all chapter headings
            for (Pattern pattern : CHAPTER_PATTERNS) {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                matches.add(new ChapterMatch(matcher.start(), matcher.end(), matcher.group().trim()));
            }
        }
        
        // Sort matches by position
        matches.sort(Comparator.comparingInt(m -> m.start));
        
        // Remove duplicates (overlapping matches)
        List<ChapterMatch> uniqueMatches = new ArrayList<>();
        for (ChapterMatch match : matches) {
            boolean isDuplicate = false;
            for (ChapterMatch existing : uniqueMatches) {
                if (Math.abs(match.start - existing.start) < 50) { // Within 50 characters
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate) {
                uniqueMatches.add(match);
            }
        }
        
        // Extract chapter content
        for (int i = 0; i < uniqueMatches.size(); i++) {
            ChapterMatch currentMatch = uniqueMatches.get(i);
            int contentStart = currentMatch.end;
            int contentEnd = (i + 1 < uniqueMatches.size()) ? 
                           uniqueMatches.get(i + 1).start : content.length();
            
            String chapterContent = content.substring(contentStart, contentEnd).trim();
            
            if (!chapterContent.isEmpty()) {
                chapters.add(new Chapter(
                    UUID.randomUUID().toString(),
                    currentMatch.title,
                    chapterContent,
                    i + 1
                ));
            }
        }
        
            logger.info("Chapter detection completed. Found {} chapters", chapters.size());
            return chapters;
        } catch (Exception e) {
            logger.error("Error during chapter detection", e);
            // Return empty list on error - the calling method will handle creating a single chapter
            return new ArrayList<>();
        }
    }
    
    public Chapter getChapter(String sessionId, String chapterId) {
        List<Chapter> chapters = sessionChapters.get(sessionId);
        if (chapters == null) {
            return null;
        }
        
        return chapters.stream()
                      .filter(chapter -> chapter.getId().equals(chapterId))
                      .findFirst()
                      .orElse(null);
    }
    
    public List<Chapter> getChapters(String sessionId, List<String> chapterIds) {
        List<Chapter> chapters = sessionChapters.get(sessionId);
        if (chapters == null) {
            return Collections.emptyList();
        }

        return chapters.stream()
                      .filter(chapter -> chapterIds.contains(chapter.getId()))
                      .toList();
    }

    public List<Chapter> getAllChapters(String sessionId) {
        return sessionChapters.getOrDefault(sessionId, Collections.emptyList());
    }

    public void cleanupSession(String sessionId) {
        sessionChapters.remove(sessionId);
    }
    
    private static class ChapterMatch {
        final int start;
        final int end;
        final String title;
        
        ChapterMatch(int start, int end, String title) {
            this.start = start;
            this.end = end;
            this.title = title;
        }
    }
}
