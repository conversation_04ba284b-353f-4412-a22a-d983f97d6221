@echo off
echo ========================================
echo   Debug Document Chapter Extractor
echo ========================================
echo.

REM Set environment variables
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set MAVEN_HOME=C:\Users\<USER>\maven
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\mvn\bin;%PATH%

echo Java Home: %JAVA_HOME%
echo Maven Home: %MAVEN_HOME%
echo.

REM Test Java
echo Testing Java...
"%JAVA_HOME%\bin\java.exe" -version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Java test failed
    pause
    exit /b 1
)
echo Java OK
echo.

REM Test Maven
echo Testing Maven...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Maven test failed
    pause
    exit /b 1
)
echo Maven OK
echo.

REM Navigate to project
cd document-chapter-extractor
echo Current directory: %CD%
echo.

REM Check if pom.xml exists
if not exist pom.xml (
    echo ERROR: pom.xml not found in current directory
    dir
    pause
    exit /b 1
)
echo pom.xml found
echo.

REM Try a simple Maven command first
echo Testing Maven in project directory...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" help:effective-settings -q
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Maven cannot access project
    pause
    exit /b 1
)
echo Maven can access project
echo.

REM Try to compile without running tests (faster)
echo Building application (skipping tests for speed)...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" clean compile -DskipTests -X

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo Build successful!
echo.

REM Try to run the application
echo Starting application...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" spring-boot:run -DskipTests

pause
