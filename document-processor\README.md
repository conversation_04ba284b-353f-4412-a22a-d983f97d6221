# Document Processor - Chapter & Page Grouping Tool

A complete web-based application for uploading documents (PDF, DOCX, TXT), automatically detecting chapters, and grouping content into customizable page groups for easy reading and export.

## Features

### 📄 Document Support
- **PDF Files**: Advanced text extraction using PyMuPDF and PyPDF2
- **Word Documents (.docx)**: Full paragraph and formatting extraction
- **Plain Text (.txt)**: Multiple encoding support (UTF-8, Latin-1, CP1252)
- **EPUB E-books**: Complete text extraction from e-book files
- **File Size**: Up to 50MB per document
- **Drag & Drop**: Intuitive file upload interface

### 🔍 Chapter Detection
- **Smart Pattern Recognition**: Detects various chapter formats:
  - "Chapter 1", "Chapter One", "Chapter I"
  - "1. Introduction", "2. Methods"
  - "Part 1", "Section 1"
  - Roman numerals: "I. Overview", "II. Analysis"
- **Automatic Parsing**: No manual chapter marking required
- **Fallback Support**: Creates single chapter if no patterns found

### 📖 Page Grouping
- **Customizable Groups**: Set pages per group (1-10)
- **Word-Based Pages**: Configurable words per page (100-1000, default 300)
- **Smart Splitting**: Maintains readability across page boundaries
- **Live Preview**: See grouped content before export

### 📤 Export Options
- **PDF Export**: Professional multi-page PDF with proper formatting
- **Text Export**: Clean text files with group separators
- **JSON Export**: Structured data with metadata for further processing
- **Customizable Headers**: Include/exclude chapter titles and page numbers

### 🎨 User Interface
- **Step-by-Step Workflow**: Guided 4-step process
- **Dark Mode**: Toggle between light and dark themes
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Preview**: Navigate through page groups before export

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Setup

1. **Navigate to the project directory:**
   ```bash
   cd document-processor
   ```

2. **Install Python dependencies:**
   ```bash
   cd server
   pip install -r requirements.txt
   ```

3. **Start the server:**
   ```bash
   python app.py
   ```

4. **Open your browser and go to:**
   ```
   http://localhost:5000
   ```

## Usage Guide

### Step 1: Upload Document
1. Drag and drop a PDF, DOCX, TXT, or EPUB file onto the upload area
2. Or click to browse and select a file
3. Wait for the document to be processed

### Step 2: Select Chapter
1. Review the automatically detected chapters
2. Click on a chapter to select it
3. Preview the chapter content and statistics

### Step 3: Configure Page Groups
1. Set the number of pages per group (default: 2)
2. Adjust words per page if needed (default: 300)
3. Click "Generate Page Groups" to create the groupings

### Step 4: Preview & Export
1. Navigate through the generated page groups
2. Configure export settings (headers, page numbers)
3. Choose your export format:
   - **PDF**: For printing or sharing
   - **Text**: For plain text editing
   - **JSON**: For data processing

## Technical Details

### Frontend Architecture
- **Vanilla JavaScript**: No external frameworks, pure ES6+ code
- **CSS Grid & Flexbox**: Modern responsive layout
- **jsPDF**: Client-side PDF generation
- **File API**: Modern browser file handling

### Backend Architecture
- **Flask**: Lightweight Python web framework
- **PyMuPDF**: Advanced PDF text extraction
- **python-docx**: Word document processing
- **Regular Expressions**: Pattern-based chapter detection

### Chapter Detection Patterns
The system recognizes these chapter patterns:
```regex
^chapter\s+\d+                    # Chapter 1, Chapter 2
^chapter\s+[ivxlcdm]+             # Chapter I, Chapter II
^chapter\s+[a-z]+                 # Chapter One, Chapter Two
^\d+\.\s+                         # 1. Introduction, 2. Methods
^part\s+\d+                       # Part 1, Part 2
^section\s+\d+                    # Section 1, Section 2
^[ivxlcdm]+\.\s+                  # I. Introduction, II. Methods
^\d+\s+[A-Z][a-z]+               # 1 Introduction, 2 Methods
```

### Page Calculation
- **Default**: 300 words per page (standard book page)
- **Customizable**: 100-1000 words per page
- **Group Size**: 1-10 pages per group
- **Smart Splitting**: Maintains word boundaries

## API Endpoints

### POST /api/process-document
Upload and process a document.

**Request:**
- Multipart form data with file upload
- Supported types: PDF, DOCX, TXT
- Max size: 50MB

**Response:**
```json
{
  "success": true,
  "text": "Full document text...",
  "chapters": {
    "Chapter 1: Introduction": "Chapter content...",
    "Chapter 2: Methods": "Chapter content..."
  },
  "word_count": 5000,
  "character_count": 25000
}
```

### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "document-processor"
}
```

## File Structure

```
document-processor/
├── index.html              # Main HTML structure
├── style.css               # Complete CSS styling
├── script.js               # Frontend JavaScript application
├── server/
│   ├── app.py              # Flask backend server
│   ├── requirements.txt    # Python dependencies
│   └── uploads/            # Temporary file storage
└── README.md               # This file
```

## Browser Compatibility

- **Chrome**: 80+ (recommended)
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

## Example Use Cases

### Academic Papers
- Upload research papers in PDF format
- Automatically detect sections and chapters
- Group content for easier reading and note-taking
- Export specific sections as separate documents

### Books & Novels
- Process e-books or scanned documents
- Detect chapter boundaries automatically
- Create reading groups for study sessions
- Export chapters for individual distribution

### Technical Documentation
- Process large technical manuals
- Group related sections together
- Create digestible chunks for training materials
- Export as structured JSON for further processing

## Troubleshooting

### Common Issues

**File Upload Fails:**
- Check file size (max 50MB)
- Ensure file type is PDF, DOCX, or TXT
- Try a different browser

**No Chapters Detected:**
- Document may not have standard chapter markers
- Use "Full Document" option that's automatically created
- Manually verify chapter formatting in original document

**PDF Text Extraction Issues:**
- Some PDFs may have image-based text (scanned documents)
- Try converting to Word format first
- Use OCR software before uploading

**Export Problems:**
- Ensure popup blockers are disabled
- Check browser download settings
- Try a different export format

## Contributing

This is a complete, production-ready application. For modifications:

1. **Frontend changes**: Edit `index.html`, `style.css`, or `script.js`
2. **Backend changes**: Modify `server/app.py`
3. **Chapter patterns**: Update regex patterns in `DocumentProcessor` class
4. **Export formats**: Extend export methods in JavaScript

## License

This project is provided as-is for educational and personal use.
