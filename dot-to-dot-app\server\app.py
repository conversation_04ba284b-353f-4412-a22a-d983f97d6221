"""
Dot-to-Dot Creator Backend
Flask server for image processing and dot generation
"""

import os
import base64
import io
import json
import numpy as np
import cv2
from PIL import Image
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

class ImageProcessor:
    """Handles image processing and dot generation"""
    
    def __init__(self):
        self.debug_mode = True
    
    def process_image(self, image_data, dot_count=50, edge_sensitivity=50):
        """
        Process image and generate dot-to-dot points
        
        Args:
            image_data: Base64 encoded image
            dot_count: Number of dots to generate
            edge_sensitivity: Edge detection sensitivity (1-100)
            
        Returns:
            List of dot coordinates
        """
        try:
            # Decode base64 image
            image = self.decode_base64_image(image_data)
            
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Detect edges
            edges = self.detect_edges(cv_image, edge_sensitivity)
            
            # Find contours
            contours = self.find_contours(edges)
            
            # Generate dots along contours
            dots = self.generate_dots(contours, dot_count, cv_image.shape)
            
            logger.info(f"Generated {len(dots)} dots from image")
            return dots
            
        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            raise
    
    def decode_base64_image(self, image_data):
        """Decode base64 image data"""
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]
        
        # Decode base64
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
    
    def detect_edges(self, image, sensitivity):
        """
        Detect edges using Canny edge detection
        
        Args:
            image: OpenCV image
            sensitivity: Edge sensitivity (1-100)
            
        Returns:
            Binary edge image
        """
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Calculate Canny thresholds based on sensitivity
        # Higher sensitivity = lower thresholds = more edges
        base_threshold = 100 - sensitivity
        low_threshold = max(10, base_threshold)
        high_threshold = min(255, low_threshold * 2.5)
        
        # Apply Canny edge detection
        edges = cv2.Canny(blurred, low_threshold, high_threshold)
        
        # Apply morphological operations to connect nearby edges
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        return edges
    
    def find_contours(self, edges):
        """Find contours from edge image"""
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours by area to remove noise
        min_area = 100
        filtered_contours = [c for c in contours if cv2.contourArea(c) > min_area]
        
        # Sort by area (largest first)
        filtered_contours.sort(key=cv2.contourArea, reverse=True)
        
        return filtered_contours
    
    def generate_dots(self, contours, dot_count, image_shape):
        """
        Generate evenly spaced dots along contours
        
        Args:
            contours: List of OpenCV contours
            dot_count: Target number of dots
            image_shape: Image dimensions
            
        Returns:
            List of dot coordinates
        """
        if not contours:
            # If no contours found, generate a simple grid
            return self.generate_grid_dots(dot_count, image_shape)
        
        dots = []
        
        # Calculate total perimeter of all contours
        total_perimeter = sum(cv2.arcLength(contour, True) for contour in contours)
        
        if total_perimeter == 0:
            return self.generate_grid_dots(dot_count, image_shape)
        
        # Distribute dots among contours based on their perimeter
        for contour in contours:
            contour_perimeter = cv2.arcLength(contour, True)
            contour_dots = max(1, int((contour_perimeter / total_perimeter) * dot_count))
            
            # Generate dots along this contour
            contour_dots_list = self.generate_contour_dots(contour, contour_dots)
            dots.extend(contour_dots_list)
            
            if len(dots) >= dot_count:
                break
        
        # Trim to exact count and ensure we have at least some dots
        if len(dots) > dot_count:
            dots = dots[:dot_count]
        elif len(dots) == 0:
            dots = self.generate_grid_dots(dot_count, image_shape)
        
        return dots
    
    def generate_contour_dots(self, contour, num_dots):
        """Generate evenly spaced dots along a single contour"""
        if len(contour) < 2:
            return []
        
        # Flatten contour points
        points = contour.reshape(-1, 2)
        
        # Calculate cumulative distances along contour
        distances = [0]
        for i in range(1, len(points)):
            dist = np.linalg.norm(points[i] - points[i-1])
            distances.append(distances[-1] + dist)
        
        total_length = distances[-1]
        if total_length == 0:
            return []
        
        # Generate evenly spaced dots
        dots = []
        for i in range(num_dots):
            target_distance = (i / max(1, num_dots - 1)) * total_length
            
            # Find the segment containing this distance
            for j in range(len(distances) - 1):
                if distances[j] <= target_distance <= distances[j + 1]:
                    # Interpolate between points
                    segment_ratio = (target_distance - distances[j]) / (distances[j + 1] - distances[j])
                    point = points[j] + segment_ratio * (points[j + 1] - points[j])
                    dots.append({'x': float(point[0]), 'y': float(point[1])})
                    break
        
        return dots
    
    def generate_grid_dots(self, dot_count, image_shape):
        """Generate a simple grid of dots as fallback"""
        height, width = image_shape[:2]
        
        # Calculate grid dimensions
        aspect_ratio = width / height
        rows = int(np.sqrt(dot_count / aspect_ratio))
        cols = int(dot_count / rows)
        
        dots = []
        for i in range(rows):
            for j in range(cols):
                if len(dots) >= dot_count:
                    break
                
                x = (j + 0.5) * width / cols
                y = (i + 0.5) * height / rows
                dots.append({'x': float(x), 'y': float(y)})
        
        return dots

# Initialize image processor
processor = ImageProcessor()

@app.route('/')
def index():
    """Serve the main application"""
    return send_from_directory('..', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('..', filename)

@app.route('/api/generate-dots', methods=['POST'])
def generate_dots():
    """Generate dot-to-dot from uploaded image"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'success': False, 'error': 'No image data provided'}), 400
        
        image_data = data['image']
        dot_count = data.get('dotCount', 50)
        edge_sensitivity = data.get('edgeSensitivity', 50)
        
        # Validate parameters
        dot_count = max(10, min(300, int(dot_count)))
        edge_sensitivity = max(1, min(100, int(edge_sensitivity)))
        
        # Process image
        dots = processor.process_image(image_data, dot_count, edge_sensitivity)
        
        return jsonify({
            'success': True,
            'dots': dots,
            'count': len(dots)
        })
        
    except Exception as e:
        logger.error(f"Error in generate_dots: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'dot-to-dot-creator'})

if __name__ == '__main__':
    print("Starting Dot-to-Dot Creator Server...")
    print("Frontend available at: http://localhost:5000")
    print("API available at: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
