<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Chapter Extractor - Demo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { background-color: #f8f9fa; }
        .drop-zone {
            background-color: #fff;
            border: 2px dashed #dee2e6;
            border-radius: 0.5rem;
            cursor: pointer;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: all 0.15s ease-in-out;
        }
        .drop-zone:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .chapter-item {
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .chapter-item:hover {
            background-color: #f8f9fa;
        }
        .demo-note {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="bg-primary text-white py-4 mb-4">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="mb-0">
                            <i class="fas fa-book-open me-2"></i>
                            Document Chapter Extractor
                        </h1>
                        <p class="mb-0 opacity-75">Extract individual chapters from your documents</p>
                    </div>
                </div>
            </div>
        </header>

        <div class="container">
            <!-- Demo Notice -->
            <div class="demo-note text-center">
                <h4><i class="fas fa-info-circle me-2"></i>Demo Interface</h4>
                <p class="mb-0">This is a preview of the Document Chapter Extractor interface. To use the full functionality, please install Java 17 and Maven, then run the application.</p>
            </div>

            <!-- Upload Section -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                Upload Document
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Drag and Drop Zone -->
                            <div class="drop-zone text-center p-5 border-2 border-dashed rounded">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">Drag & Drop your document here</h4>
                                <p class="text-muted mb-3">or click to browse files</p>
                                <button type="button" class="btn btn-primary" onclick="showDemo()">
                                    <i class="fas fa-folder-open me-2"></i>Browse Files (Demo)
                                </button>
                            </div>
                            
                            <!-- Supported Formats -->
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <strong>Supported formats:</strong> PDF, EPUB, DOC, DOCX, TXT, ODT, RTF
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Demo Results Section -->
            <div class="row justify-content-center" id="demoResults" style="display: none;">
                <div class="col-lg-10">
                    <!-- Document Info -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                Processing Complete
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>File:</strong> sample-book.txt</p>
                                    <p class="mb-1"><strong>Type:</strong> Text Document</p>
                                    <p class="mb-0"><strong>Size:</strong> 15.2 KB</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Chapters Found:</strong> 5</p>
                                    <p class="mb-1"><strong>Total Words:</strong> 2,847</p>
                                    <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download all chapters as ZIP')">
                                        <i class="fas fa-download me-1"></i>Download All as ZIP
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chapters List -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                Extracted Chapters
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <!-- Chapter 1 -->
                                <div class="chapter-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h5 class="chapter-title">Chapter 1: The Beginning</h5>
                                            <div class="chapter-meta text-muted mb-2">
                                                <i class="fas fa-align-left me-1"></i>487 words
                                                <span class="ms-3"><i class="fas fa-hashtag me-1"></i>Chapter 1</span>
                                            </div>
                                            <div class="chapter-preview text-muted fst-italic">
                                                This is the first chapter of our sample book. It introduces the main characters and sets the stage for the adventure that is about to unfold...
                                            </div>
                                        </div>
                                        <div class="chapter-actions ms-3">
                                            <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download Chapter 1 as text file')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chapter 2 -->
                                <div class="chapter-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h5 class="chapter-title">Chapter 2: Preparation and Planning</h5>
                                            <div class="chapter-meta text-muted mb-2">
                                                <i class="fas fa-align-left me-1"></i>523 words
                                                <span class="ms-3"><i class="fas fa-hashtag me-1"></i>Chapter 2</span>
                                            </div>
                                            <div class="chapter-preview text-muted fst-italic">
                                                In the second chapter, Alex begins preparing for the expedition. They research the history of the area shown on the map...
                                            </div>
                                        </div>
                                        <div class="chapter-actions ms-3">
                                            <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download Chapter 2 as text file')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chapter 3 -->
                                <div class="chapter-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h5 class="chapter-title">Chapter 3: Into the Wilderness</h5>
                                            <div class="chapter-meta text-muted mb-2">
                                                <i class="fas fa-align-left me-1"></i>612 words
                                                <span class="ms-3"><i class="fas fa-hashtag me-1"></i>Chapter 3</span>
                                            </div>
                                            <div class="chapter-preview text-muted fst-italic">
                                                The third chapter begins with Alex and Sam setting out on their journey at dawn. The early morning mist clings to the trees...
                                            </div>
                                        </div>
                                        <div class="chapter-actions ms-3">
                                            <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download Chapter 3 as text file')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chapter 4 -->
                                <div class="chapter-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h5 class="chapter-title">Chapter 4: The Discovery</h5>
                                            <div class="chapter-meta text-muted mb-2">
                                                <i class="fas fa-align-left me-1"></i>598 words
                                                <span class="ms-3"><i class="fas fa-hashtag me-1"></i>Chapter 4</span>
                                            </div>
                                            <div class="chapter-preview text-muted fst-italic">
                                                The fourth chapter is the climax of their adventure. After a challenging morning of hiking through increasingly difficult terrain...
                                            </div>
                                        </div>
                                        <div class="chapter-actions ms-3">
                                            <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download Chapter 4 as text file')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chapter 5 -->
                                <div class="chapter-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h5 class="chapter-title">Chapter 5: The Return</h5>
                                            <div class="chapter-meta text-muted mb-2">
                                                <i class="fas fa-align-left me-1"></i>627 words
                                                <span class="ms-3"><i class="fas fa-hashtag me-1"></i>Chapter 5</span>
                                            </div>
                                            <div class="chapter-preview text-muted fst-italic">
                                                The final chapter follows Alex and Sam as they carefully pack their discoveries and begin the journey back to civilization...
                                            </div>
                                        </div>
                                        <div class="chapter-actions ms-3">
                                            <button class="btn btn-primary btn-sm" onclick="alert('Demo: Would download Chapter 5 as text file')">
                                                <i class="fas fa-download me-1"></i>Download
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Installation Instructions -->
            <div class="row justify-content-center mt-4">
                <div class="col-lg-8">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h4 class="mb-0"><i class="fas fa-download me-2"></i>How to Run the Full Application</h4>
                        </div>
                        <div class="card-body">
                            <h5>Prerequisites:</h5>
                            <ol>
                                <li><strong>Install Java 17:</strong> Download from <a href="https://adoptium.net/" target="_blank">Adoptium</a></li>
                                <li><strong>Install Maven:</strong> Download from <a href="https://maven.apache.org/download.cgi" target="_blank">Apache Maven</a></li>
                            </ol>
                            
                            <h5>Run Commands:</h5>
                            <div class="bg-dark text-light p-3 rounded">
                                <code>
                                    cd document-chapter-extractor<br>
                                    mvn clean install<br>
                                    mvn spring-boot:run
                                </code>
                            </div>
                            
                            <h5 class="mt-3">Then access:</h5>
                            <p><strong>http://localhost:8080</strong></p>
                            
                            <div class="alert alert-success">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Tip:</strong> You can also open the project in IntelliJ IDEA, Eclipse, or VS Code and run it directly from the IDE!
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDemo() {
            document.getElementById('demoResults').style.display = 'block';
            document.getElementById('demoResults').scrollIntoView({ behavior: 'smooth' });
            
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-info alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                Demo: Showing sample chapter extraction results
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 4000);
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
