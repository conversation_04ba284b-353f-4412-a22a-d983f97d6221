# Java Installation Script for Windows
Write-Host "☕ Installing Java 17 for Document Chapter Extractor..." -ForegroundColor Green
Write-Host ""

# Java download URL (Eclipse Temurin OpenJDK 17)
$javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.msi"
$javaInstaller = "$env:TEMP\OpenJDK17-installer.msi"
$javaHome = "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot"

Write-Host "📥 Downloading Java 17..." -ForegroundColor Cyan
Write-Host "   URL: $javaUrl" -ForegroundColor Gray
Write-Host "   Saving to: $javaInstaller" -ForegroundColor Gray
Write-Host ""

try {
    # Download Java installer
    Invoke-WebRequest -Uri $javaUrl -OutFile $javaInstaller -UseBasicParsing
    Write-Host "✅ Java installer downloaded successfully" -ForegroundColor Green
    Write-Host ""

    # Install Java silently
    Write-Host "🔧 Installing Java 17..." -ForegroundColor Cyan
    Write-Host "   This may take a few minutes..." -ForegroundColor Yellow
    
    $installProcess = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$javaInstaller`" /quiet /norestart" -Wait -PassThru
    
    if ($installProcess.ExitCode -eq 0) {
        Write-Host "✅ Java 17 installed successfully!" -ForegroundColor Green
        Write-Host ""
        
        # Set JAVA_HOME environment variable
        Write-Host "🔧 Setting JAVA_HOME environment variable..." -ForegroundColor Cyan
        
        # Find the actual Java installation path
        $possiblePaths = @(
            "C:\Program Files\Eclipse Adoptium\jdk-********-hotspot",
            "C:\Program Files\Eclipse Adoptium\jdk-17*",
            "C:\Program Files\Java\jdk-17*"
        )
        
        $actualJavaHome = $null
        foreach ($path in $possiblePaths) {
            $resolved = Get-ChildItem -Path (Split-Path $path) -Directory -Name (Split-Path $path -Leaf) -ErrorAction SilentlyContinue | Select-Object -First 1
            if ($resolved) {
                $actualJavaHome = Join-Path (Split-Path $path) $resolved
                break
            }
        }
        
        if (-not $actualJavaHome) {
            # Try to find any Java 17 installation
            $javaInstalls = Get-ChildItem -Path "C:\Program Files" -Directory -Name "*jdk*17*" -ErrorAction SilentlyContinue
            if ($javaInstalls) {
                $actualJavaHome = Join-Path "C:\Program Files" $javaInstalls[0]
            }
        }
        
        if ($actualJavaHome -and (Test-Path $actualJavaHome)) {
            Write-Host "   Java found at: $actualJavaHome" -ForegroundColor White
            
            # Set system environment variables
            [Environment]::SetEnvironmentVariable("JAVA_HOME", $actualJavaHome, [EnvironmentVariableTarget]::Machine)
            
            # Add Java to PATH
            $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
            $javaBin = Join-Path $actualJavaHome "bin"
            
            if ($currentPath -notlike "*$javaBin*") {
                $newPath = $currentPath + ";" + $javaBin
                [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
                Write-Host "✅ Java added to system PATH" -ForegroundColor Green
            }
            
            Write-Host "✅ JAVA_HOME set to: $actualJavaHome" -ForegroundColor Green
            
            # Test Java installation
            Write-Host ""
            Write-Host "🧪 Testing Java installation..." -ForegroundColor Cyan
            
            $env:JAVA_HOME = $actualJavaHome
            $env:PATH = $env:PATH + ";" + $javaBin
            
            $javaVersion = & "$javaBin\java.exe" -version 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Java is working correctly!" -ForegroundColor Green
                Write-Host $javaVersion -ForegroundColor White
            }
            
        } else {
            Write-Host "⚠️  Could not find Java installation directory" -ForegroundColor Yellow
            Write-Host "   Please set JAVA_HOME manually to your Java installation directory" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "❌ Java installation failed with exit code: $($installProcess.ExitCode)" -ForegroundColor Red
    }
    
    # Clean up installer
    if (Test-Path $javaInstaller) {
        Remove-Item $javaInstaller -Force
        Write-Host "🗑️  Cleaned up installer file" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ Error during Java installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Manual installation steps:" -ForegroundColor Yellow
    Write-Host "   1. Go to https://adoptium.net/" -ForegroundColor White
    Write-Host "   2. Download Java 17 (LTS)" -ForegroundColor White
    Write-Host "   3. Run the installer" -ForegroundColor White
    Write-Host "   4. Restart your command prompt" -ForegroundColor White
}

Write-Host ""
Write-Host "🔄 Please restart your command prompt to use the updated environment variables" -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 After restarting, you can run the Document Chapter Extractor:" -ForegroundColor Cyan
Write-Host "   cd document-chapter-extractor" -ForegroundColor White
Write-Host "   mvn clean install" -ForegroundColor White
Write-Host "   mvn spring-boot:run" -ForegroundColor White
Write-Host "   Open browser to: http://localhost:8080" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
