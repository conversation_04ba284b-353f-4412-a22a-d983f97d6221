#!/usr/bin/env python3
"""
Dot-to-Dot Image Converter Backend Server
A Flask-based backend for advanced image processing and dot-to-dot conversion.
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io
import base64
import os
from werkzeug.utils import secure_filename
import tempfile
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def decode_base64_image(base64_string):
    """Decode base64 image string to numpy array."""
    try:
        # Remove data URL prefix if present
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]
        
        # Decode base64
        image_data = base64.b64decode(base64_string)
        
        # Convert to PIL Image
        pil_image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # Convert to numpy array
        numpy_image = np.array(pil_image)
        
        return numpy_image
    except Exception as e:
        logger.error(f"Error decoding base64 image: {e}")
        return None

def advanced_edge_detection(image, sensitivity=50):
    """
    Direct outline detection using thresholding and contour-based approach.
    """
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        print(f"Backend: Starting direct outline detection with sensitivity {sensitivity}")
        print(f"Backend: Image shape: {gray.shape}, dtype: {gray.dtype}")

        # Apply strong denoising
        denoised = cv2.fastNlMeansDenoising(gray, None, 10, 7, 21)

        # Apply adaptive thresholding to create binary image
        # This will separate foreground objects from background
        binary = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # Invert if needed (we want objects to be white)
        if np.mean(binary) > 127:
            binary = cv2.bitwise_not(binary)

        # Apply morphological operations to clean up the binary image
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

        # Remove small noise
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # Fill small holes
        filled = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel)

        # Find contours of the objects
        contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        # Create edge image by drawing contour outlines
        edges = np.zeros_like(gray)

        if contours:
            # Filter contours by area
            min_area = (gray.shape[0] * gray.shape[1]) * 0.001  # At least 0.1% of image
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]

            print(f"Backend: Found {len(valid_contours)} valid contours from {len(contours)} total")

            if valid_contours:
                # Draw contour outlines
                cv2.drawContours(edges, valid_contours, -1, 255, 1)

                # Also try multiple Canny settings for additional edges
                canny1 = cv2.Canny(denoised, 30, 100)
                canny2 = cv2.Canny(denoised, 50, 150)
                canny3 = cv2.Canny(denoised, 10, 50)  # Very sensitive

                # Combine all approaches
                combined = cv2.bitwise_or(edges, canny1)
                combined = cv2.bitwise_or(combined, canny2)
                combined = cv2.bitwise_or(combined, canny3)

                # Clean up the combined result
                final_edges = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
            else:
                # Fallback to aggressive Canny
                print("Backend: No valid contours, using aggressive Canny")
                final_edges = cv2.Canny(denoised, 20, 60)
        else:
            # Fallback to aggressive Canny
            print("Backend: No contours found, using aggressive Canny")
            final_edges = cv2.Canny(denoised, 20, 60)

        # Count edge pixels for debugging
        edge_pixels = np.sum(final_edges > 0)
        total_pixels = final_edges.shape[0] * final_edges.shape[1]
        edge_percentage = (edge_pixels / total_pixels) * 100

        print(f"Backend: Edge detection complete - {edge_pixels} edge pixels ({edge_percentage:.2f}% of image)")

        return final_edges

    except Exception as e:
        logger.error(f"Error in edge detection: {e}")
        # Simple fallback
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            return edges
        except:
            return None

def extract_contours(edges):
    """Extract main outline contours from edge image."""
    try:
        # Find all contours without approximation to preserve detail
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

        if not contours:
            print("Backend: No contours found in edge image")
            return []

        print(f"Backend: Found {len(contours)} raw contours")

        # Filter contours by size and shape
        valid_contours = []

        for contour in contours:
            # Calculate basic properties
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            # Filter by minimum size - be more lenient to catch more outlines
            if area < 20 or perimeter < 20:
                continue

            # Filter by shape - remove very thin/elongated shapes that are likely noise
            if perimeter > 0:
                # Calculate circularity-like measure
                compactness = (4 * np.pi * area) / (perimeter * perimeter)

                # Accept a wide range of shapes (not just circular)
                if compactness > 0.01:  # Very lenient threshold
                    valid_contours.append(contour)

        if not valid_contours:
            print("Backend: No valid contours after filtering")
            # If no valid contours, return the largest raw contours
            contours_by_area = sorted(contours, key=cv2.contourArea, reverse=True)
            return contours_by_area[:5]  # Return top 5 largest

        # Sort by perimeter (outline length) - longer outlines are more important
        valid_contours.sort(key=lambda c: cv2.arcLength(c, True), reverse=True)

        print(f"Backend: Extracted {len(valid_contours)} valid contours")

        # Log details about top contours
        for i, contour in enumerate(valid_contours[:3]):
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            print(f"Backend: Contour {i+1}: area={area:.1f}, perimeter={perimeter:.1f}, points={len(contour)}")

        return valid_contours

    except Exception as e:
        logger.error(f"Error extracting contours: {e}")
        return []

def select_optimal_points(contours, target_count):
    """Select points that follow the main outlines closely."""
    try:
        if not contours:
            print("Backend: No contours provided for point selection")
            return []

        print(f"Backend: Selecting {target_count} points from {len(contours)} contours")

        # Focus on the most significant contours (top 3 by perimeter)
        contours_with_perimeter = [(c, cv2.arcLength(c, True)) for c in contours]
        contours_with_perimeter.sort(key=lambda x: x[1], reverse=True)

        # Use top contours that have substantial perimeter
        main_contours = []
        total_perimeter = 0

        for contour, perimeter in contours_with_perimeter[:5]:  # Top 5 contours
            if perimeter > 30:  # Minimum meaningful perimeter
                main_contours.append((contour, perimeter))
                total_perimeter += perimeter

        if not main_contours:
            print("Backend: No substantial contours found")
            return []

        print(f"Backend: Using {len(main_contours)} main contours with total perimeter {total_perimeter:.1f}")

        selected_points = []
        remaining_count = target_count

        # Distribute points among main contours based on their perimeter
        for i, (contour, perimeter) in enumerate(main_contours):
            # Calculate points for this contour
            if i == len(main_contours) - 1:
                # Last contour gets all remaining points
                points_for_contour = remaining_count
            else:
                # Proportional allocation based on perimeter
                proportion = perimeter / total_perimeter
                points_for_contour = max(3, int(proportion * target_count))
                points_for_contour = min(points_for_contour, remaining_count)

            print(f"Backend: Contour {i+1}: perimeter={perimeter:.1f}, allocating {points_for_contour} points")

            # Select evenly spaced points along this contour
            contour_points = select_evenly_spaced_points_on_contour(contour, points_for_contour)
            selected_points.extend(contour_points)
            remaining_count -= len(contour_points)

            if remaining_count <= 0:
                break

        print(f"Backend: Selected {len(selected_points)} points total")
        return selected_points[:target_count]  # Ensure exact count

    except Exception as e:
        logger.error(f"Error selecting optimal points: {e}")
        return []

def select_evenly_spaced_points_on_contour(contour, target_count):
    """Select evenly spaced points along a single contour with high precision."""
    try:
        if len(contour) <= target_count:
            return [(int(point[0][0]), int(point[0][1])) for point in contour]

        # Flatten contour points for easier processing
        points = contour.reshape(-1, 2)

        # Calculate cumulative distances along the contour
        distances = [0]
        for i in range(1, len(points)):
            dist = np.sqrt(np.sum((points[i] - points[i-1])**2))
            distances.append(distances[-1] + dist)

        # Add distance back to start for closed contour
        if len(points) > 2:
            dist = np.sqrt(np.sum((points[0] - points[-1])**2))
            distances.append(distances[-1] + dist)

        total_perimeter = distances[-1]
        if total_perimeter == 0:
            return [(int(points[0][0]), int(points[0][1]))]

        # Calculate spacing between points for even distribution
        spacing = total_perimeter / target_count

        selected_points = []

        # Generate evenly spaced target distances
        for i in range(target_count):
            target_distance = i * spacing

            # Find the segment containing this target distance
            for j in range(len(distances) - 1):
                if distances[j] <= target_distance <= distances[j + 1]:
                    # Interpolate between the two points
                    if distances[j + 1] - distances[j] > 0:
                        t = (target_distance - distances[j]) / (distances[j + 1] - distances[j])

                        # Get the two points to interpolate between
                        if j < len(points):
                            p1 = points[j]
                            p2 = points[(j + 1) % len(points)]  # Handle wrap-around for closed contour

                            # Linear interpolation
                            x = int(p1[0] + t * (p2[0] - p1[0]))
                            y = int(p1[1] + t * (p2[1] - p1[1]))
                            selected_points.append((x, y))
                    break

        # Ensure we have the requested number of points
        if len(selected_points) < target_count and len(points) > 0:
            # Fill remaining with points from the contour
            remaining = target_count - len(selected_points)
            step = max(1, len(points) // remaining)
            for i in range(0, len(points), step):
                if len(selected_points) >= target_count:
                    break
                selected_points.append((int(points[i][0]), int(points[i][1])))

        return selected_points[:target_count]

    except Exception as e:
        logger.error(f"Error selecting evenly spaced points: {e}")
        return [(int(contour[0][0][0]), int(contour[0][0][1]))] if len(contour) > 0 else []

def order_points_for_drawing(points):
    """Order points for logical dot-to-dot sequence."""
    try:
        if len(points) <= 1:
            return points
        
        ordered_points = []
        remaining_points = points.copy()
        
        # Start with the top-left most point
        current = min(remaining_points, key=lambda p: p[0] + p[1])
        ordered_points.append(current)
        remaining_points.remove(current)
        
        # Use nearest neighbor algorithm
        while remaining_points:
            nearest = min(remaining_points, key=lambda p: 
                         (current[0] - p[0])**2 + (current[1] - p[1])**2)
            ordered_points.append(nearest)
            remaining_points.remove(nearest)
            current = nearest
        
        return ordered_points
    except Exception as e:
        logger.error(f"Error ordering points: {e}")
        return points

def create_dot_to_dot_image(points, image_size, show_connections=True):
    """Create the final dot-to-dot image."""
    try:
        # Create white background
        img = Image.new('RGB', image_size, 'white')
        draw = ImageDraw.Draw(img)
        
        # Try to load a font, fallback to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        # Draw connecting lines if requested
        if show_connections and len(points) > 1:
            for i in range(len(points) - 1):
                draw.line([points[i], points[i + 1]], fill='lightgray', width=1)
        
        # Draw dots and numbers
        for i, (x, y) in enumerate(points):
            # Draw dot
            draw.ellipse([x-4, y-4, x+4, y+4], fill='black')
            
            # Draw number background
            draw.ellipse([x+10, y-20, x+30, y], fill='white', outline='black')
            
            # Draw number
            draw.text((x+20, y-10), str(i+1), fill='black', font=font, anchor='mm')
        
        return img
    except Exception as e:
        logger.error(f"Error creating dot-to-dot image: {e}")
        return None

@app.route('/', methods=['GET'])
def index():
    """Health check endpoint."""
    return jsonify({
        'status': 'success',
        'message': 'Dot-to-Dot Converter Backend is running!',
        'version': '1.0.0'
    })

@app.route('/process', methods=['POST'])
def process_image():
    """Main endpoint for processing images into dot-to-dot."""
    try:
        print("Backend: ===== RECEIVED IMAGE PROCESSING REQUEST =====")

        # Get request data
        data = request.get_json()

        if not data:
            print("Backend: ERROR - No data provided")
            return jsonify({'error': 'No data provided'}), 400
        
        # Extract parameters
        image_data = data.get('image')
        dot_count = int(data.get('dotCount', 50))
        edge_sensitivity = int(data.get('edgeSensitivity', 30))
        show_connections = data.get('showConnections', True)

        print(f"Backend: Parameters - dotCount: {dot_count}, edgeSensitivity: {edge_sensitivity}")
        print(f"Backend: Image data length: {len(image_data) if image_data else 'None'}")

        if not image_data:
            print("Backend: ERROR - No image data provided")
            return jsonify({'error': 'No image data provided'}), 400
        
        # Decode image
        print("Backend: Decoding base64 image...")
        image = decode_base64_image(image_data)
        if image is None:
            print("Backend: ERROR - Failed to decode image")
            return jsonify({'error': 'Invalid image data'}), 400

        print(f"Backend: Image decoded successfully - shape: {image.shape}")
        
        # Save debug images to see what's happening
        debug_dir = 'debug_images'
        os.makedirs(debug_dir, exist_ok=True)

        # Save original image
        cv2.imwrite(f'{debug_dir}/01_original.png', image)

        # Process image with enhanced edge detection
        edges = advanced_edge_detection(image, edge_sensitivity)
        if edges is None:
            return jsonify({'error': 'Edge detection failed'}), 500

        # Save edge detection result
        cv2.imwrite(f'{debug_dir}/02_edges.png', edges)

        contours = extract_contours(edges)
        if not contours:
            return jsonify({'error': 'No contours found'}), 400

        # Save contours visualization
        contour_img = image.copy()
        cv2.drawContours(contour_img, contours, -1, (0, 255, 0), 2)
        cv2.imwrite(f'{debug_dir}/03_contours.png', contour_img)

        points = select_optimal_points(contours, dot_count)
        if not points:
            return jsonify({'error': 'No points selected'}), 400

        # Save points visualization
        points_img = image.copy()
        for i, (x, y) in enumerate(points):
            cv2.circle(points_img, (x, y), 3, (255, 0, 0), -1)
            cv2.putText(points_img, str(i+1), (x+5, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
        cv2.imwrite(f'{debug_dir}/04_points.png', points_img)

        ordered_points = order_points_for_drawing(points)

        print(f"Backend: Debug images saved to {debug_dir}/ folder")
        
        # Create dot-to-dot image
        dot_to_dot_img = create_dot_to_dot_image(
            ordered_points, 
            (image.shape[1], image.shape[0]), 
            show_connections
        )
        
        if dot_to_dot_img is None:
            return jsonify({'error': 'Failed to create dot-to-dot image'}), 500
        
        # Convert to base64 for response
        buffer = io.BytesIO()
        dot_to_dot_img.save(buffer, format='PNG')
        buffer.seek(0)
        
        result_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return jsonify({
            'status': 'success',
            'image': f'data:image/png;base64,{result_base64}',
            'dotCount': len(ordered_points),
            'points': ordered_points
        })
        
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    """Alternative file upload endpoint."""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)
        
        return jsonify({
            'status': 'success',
            'message': 'File uploaded successfully',
            'filename': filename
        })
        
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

if __name__ == '__main__':
    print("🎯 Starting Dot-to-Dot Converter Backend Server...")
    print("📡 Server will be available at: http://localhost:5000")
    print("🔗 Frontend should connect to this URL for advanced processing")
    app.run(debug=True, host='0.0.0.0', port=5000)
