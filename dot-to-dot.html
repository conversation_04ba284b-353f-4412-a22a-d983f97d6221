<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dot-to-Dot Creator</title>
    <!-- Include image-js library for advanced image processing -->
    <script src="https://unpkg.com/image-js@0.35.6/dist/image.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        #imageUpload {
            padding: 8px;
            margin-right: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: white;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        .canvas-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .canvas-wrapper {
            flex: 1;
            position: relative;
            border: 2px dashed #ccc;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .canvas-wrapper:first-child::after {
            content: 'Drop image here';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #999;
            font-size: 1.2em;
            pointer-events: none;
            opacity: 0.7;
        }

        canvas {
            border: 2px solid #333;
            background-color: white;
            cursor: crosshair;
        }

        .canvas-wrapper.drag-over {
            border-color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
        }

        .canvas-wrapper:first-child.drag-over::after {
            content: 'Release to upload';
            color: #4CAF50;
        }

        .canvas-wrapper.drag-over canvas {
            border: 2px dashed #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #45a049;
        }

        button.clear {
            background-color: #f44336;
        }

        button.clear:hover {
            background-color: #da190b;
        }

        .instructions {
            background-color: #e9f5e9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .instructions h2 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .instructions ul {
            list-style-position: inside;
            color: #1b5e20;
        }

        #dotCount {
            padding: 8px;
            width: 80px;
            margin-right: 10px;
        }

        .drag-over {
            border: 3px dashed #4CAF50 !important;
            background-color: rgba(76, 175, 80, 0.1) !important;
        }

        .canvas-wrapper {
            border: 2px dashed #ccc;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dot-to-Dot Creator</h1>

        <div class="instructions">
            <h2>How to Use:</h2>
            <ul>
                <li>Upload an image (drag & drop or use the upload button):</li>
                <ul style="margin-left: 20px;">
                    <li>Supported formats: JPG, PNG, GIF, BMP, WebP</li>
                    <li>Maximum file size: 5MB</li>
                </ul>
                <li>Or draw your picture directly on the left canvas</li>
                <li>Adjust the number of dots you want (5-100)</li>
                <li>Click "Convert to Dot-to-Dot" to see the result</li>
                <li>Use "Clear" to start over</li>
            </ul>
        </div>

        <div class="controls">
            <input type="file" id="imageUpload" accept="image/*">
            <input type="number" id="dotCount" value="20" min="5" max="100" step="1">
            <button id="convertBtn">Convert to Dot-to-Dot</button>
            <button class="clear" id="clearBtn">Clear</button>
        </div>

        <div class="canvas-container">
            <div class="canvas-wrapper" id="dropZone">
                <canvas id="drawCanvas" width="500" height="500"></canvas>
            </div>
            <div class="canvas-wrapper">
                <canvas id="dotCanvas" width="500" height="500"></canvas>
            </div>
        </div>
    </div>

    <script>
        const drawCanvas = document.getElementById('drawCanvas');
        const dotCanvas = document.getElementById('dotCanvas');
        const drawCtx = drawCanvas.getContext('2d');
        const dotCtx = dotCanvas.getContext('2d');
        const convertBtn = document.getElementById('convertBtn');
        const clearBtn = document.getElementById('clearBtn');
        const dotCountInput = document.getElementById('dotCount');

        // Add clear button event listener
        clearBtn.addEventListener('click', clearCanvas);

        let isDrawing = false;
        let lastX = 0;
        let lastY = 0;
        let points = [];
        let uploadedImage = null;

        // Drawing setup
        drawCtx.strokeStyle = '#000';
        drawCtx.lineWidth = 2;
        drawCtx.lineCap = 'round';

        // Event listeners for drawing (mouse)
        drawCanvas.addEventListener('mousedown', startDrawing);
        drawCanvas.addEventListener('mousemove', draw);
        drawCanvas.addEventListener('mouseup', stopDrawing);
        drawCanvas.addEventListener('mouseout', stopDrawing);

        // Event listeners for drawing (touch)
        drawCanvas.addEventListener('touchstart', handleTouch);
        drawCanvas.addEventListener('touchmove', handleTouch);
        drawCanvas.addEventListener('touchend', stopDrawing);

        function startDrawing(e) {
            isDrawing = true;
            [lastX, lastY] = getMousePos(drawCanvas, e);
            points.push({ x: lastX, y: lastY });
        }

        function draw(e) {
            if (!isDrawing) return;

            const [x, y] = getMousePos(drawCanvas, e);
            drawCtx.beginPath();
            drawCtx.moveTo(lastX, lastY);
            drawCtx.lineTo(x, y);
            drawCtx.stroke();

            points.push({ x, y });
            [lastX, lastY] = [x, y];
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function getMousePos(canvas, e) {
            const rect = canvas.getBoundingClientRect();
            return [
                e.clientX - rect.left,
                e.clientY - rect.top
            ];
        }

        function handleTouch(e) {
            e.preventDefault(); // Prevent scrolling
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });

            if (e.type === 'touchstart') {
                startDrawing(mouseEvent);
            } else if (e.type === 'touchmove') {
                draw(mouseEvent);
            }
        }

        function clearCanvas() {
            drawCtx.clearRect(0, 0, drawCanvas.width, drawCanvas.height);
            dotCtx.clearRect(0, 0, dotCanvas.width, dotCanvas.height);
            points = [];
            uploadedImage = null;
            window.originalImageData = null;
            window.originalImageDimensions = null;

            // Reset file input
            const fileInput = document.getElementById('imageUpload');
            if (fileInput) {
                fileInput.value = '';
            }

            // Clear status
            updateStatus('Ready to upload image or draw manually');
        }

        // Handle drag and drop
        const dropZone = document.getElementById('dropZone');

        // Prevent default drag behaviors
        document.addEventListener('dragenter', preventDefaults, false);
        document.addEventListener('dragover', preventDefaults, false);
        document.addEventListener('dragleave', preventDefaults, false);
        document.addEventListener('drop', preventDefaults, false);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // Handle drag events on dropzone
        dropZone.addEventListener('dragenter', (e) => {
            preventDefaults(e);
            highlight(e);
        }, false);

        dropZone.addEventListener('dragover', (e) => {
            preventDefaults(e);
            highlight(e);
        }, false);

        dropZone.addEventListener('dragleave', (e) => {
            preventDefaults(e);
            unhighlight(e);
        }, false);

        dropZone.addEventListener('drop', (e) => {
            preventDefaults(e);
            unhighlight(e);
            handleDrop(e);
        }, false);

        function highlight(e) {
            dropZone.classList.add('drag-over');
        }

        function unhighlight(e) {
            dropZone.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            const dt = e.dataTransfer;
            if (dt.files && dt.files.length > 0) {
                const file = dt.files[0];
                console.log('File dropped:', file.name);
                handleImageFile(file);
            } else {
                console.log('No file in drop event');
                updateStatus('No file detected in drop', true);
            }
        }

        // Handle image upload through input
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const file = e.target.files[0];
            if (file) {
                console.log('File selected through input:', file.name);
                handleImageFile(file);
            }
        });

        // Create status message element
        let statusDiv = document.createElement('div');
        statusDiv.id = 'statusMessage';
        statusDiv.style.marginTop = '10px';
        statusDiv.style.color = '#666';
        document.querySelector('.controls').appendChild(statusDiv);

        function updateStatus(message, isError = false) {
            statusDiv.textContent = message;
            statusDiv.style.color = isError ? '#f44336' : '#4CAF50';
        }

        // Common function to handle image files
        function handleImageFile(file) {
            console.log('File input change detected');
            if (!file) {
                console.log('No file selected');
                updateStatus('No file selected', true);
                return;
            }
            
            console.log('File selected:', file.name, 'Type:', file.type);
            
            const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
            const maxFileSize = 5 * 1024 * 1024; // 5MB

            if (file.size > maxFileSize) {
                console.log('File too large:', file.size, 'bytes');
                const errorMsg = 'Image file is too large. Please select an image under 5MB.';
                updateStatus(errorMsg, true);
                alert(errorMsg);
                if (document.getElementById('imageUpload')) {
                    document.getElementById('imageUpload').value = '';
                }
                clearCanvas();
                return;
            }

            if (!validImageTypes.includes(file.type)) {
                console.log('Invalid file type:', file.type);
                const errorMsg = 'Please select a valid image file (JPG, PNG, GIF, BMP, or WebP).';
                updateStatus(errorMsg, true);
                alert(errorMsg);
                if (document.getElementById('imageUpload')) {
                    document.getElementById('imageUpload').value = '';
                }
                clearCanvas();
                return;
            }

            console.log('Valid image file detected, starting upload process');

            try {
                const reader = new FileReader();

                reader.onerror = function(error) {
                    console.error('FileReader error:', error);
                    alert('Error reading the image file. Please try another image.');
                };

                reader.onload = function(event) {
                    console.log('File successfully read');
                    uploadedImage = new Image();
                    console.log('Setting up new image');

                    // Add a timeout to detect loading issues
                    const imageLoadTimeout = setTimeout(() => {
                        console.error('Image load timeout');
                        updateStatus('Image loading took too long. Please try again.', true);
                        clearCanvas();
                    }, 10000); // 10 second timeout
                    
                    uploadedImage.onerror = function(error) {
                        console.error('Image loading error:', error);
                        console.log('Image source that failed:', uploadedImage.src.substring(0, 100) + '...');
                        updateStatus('Error loading image. Please try another image.', true);
                        clearCanvas();
                        alert('Error loading the image. Please try another image or a different file format.');
                    };
                    
                    // Show loading indicator immediately
                    drawCtx.save();
                    drawCtx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                    drawCtx.fillRect(0, 0, drawCanvas.width, drawCanvas.height);
                    drawCtx.font = '20px Arial';
                    drawCtx.fillStyle = '#4CAF50';
                    drawCtx.textAlign = 'center';
                    drawCtx.fillText('Loading image...', drawCanvas.width/2, drawCanvas.height/2);
                    drawCtx.restore();

                    uploadedImage.onload = function() {
                        clearTimeout(imageLoadTimeout);
                        console.log('Image successfully loaded');
                        console.log('Image natural dimensions:', {
                            width: this.naturalWidth,
                            height: this.naturalHeight
                        });
                        updateStatus('Image loaded successfully, processing...');
                        
                        // Calculate scaling to fit canvas
                        let imageScale = Math.min(
                            drawCanvas.width / this.naturalWidth,
                            drawCanvas.height / this.naturalHeight
                        ) * 0.9;
                        
                        const scaledWidth = this.naturalWidth * imageScale;
                        const scaledHeight = this.naturalHeight * imageScale;
                        const x = (drawCanvas.width - scaledWidth) / 2;
                        const y = (drawCanvas.height - scaledHeight) / 2;
                        
                        // Clear canvas and draw image
                        drawCtx.clearRect(0, 0, drawCanvas.width, drawCanvas.height);
                        drawCtx.drawImage(this, x, y, scaledWidth, scaledHeight);

                        // Store original dimensions and image data for edge detection
                        window.originalImageDimensions = {
                            width: this.naturalWidth,
                            height: this.naturalHeight,
                            scale: imageScale,
                            offsetX: x,
                            offsetY: y
                        };

                        // Store the original image data for processing later
                        window.originalImageData = drawCtx.getImageData(0, 0, drawCanvas.width, drawCanvas.height);

                        console.log('Image displayed successfully on canvas');
                        updateStatus('Image loaded! Click "Convert to Dot-to-Dot" to process it.');
                        

                    };

                    try {
                        console.log('Attempting to set image source');
                        const imageData = event.target.result;

                        // Check for empty or corrupted data
                        if (imageData.length < 1000) {
                            throw new Error('Image data appears to be incomplete or corrupted');
                        }

                        // Reset points array before loading new image
                        points = [];
                        // Clear only the dot canvas, keep draw canvas for image display
                        dotCtx.clearRect(0, 0, dotCanvas.width, dotCanvas.height);

                        // Set the image source
                        console.log('Setting image source with data length:', imageData.length);
                        uploadedImage.src = imageData;
                        console.log('Image source set successfully');
                    } catch (error) {
                        console.error('Error setting image source:', error);
                        updateStatus('Error loading image: ' + error.message, true);
                        clearCanvas();
                    }
                };

                reader.readAsDataURL(file);
            } catch (error) {
                console.error('Error processing image:', error);
                updateStatus('Error processing image: ' + error.message, true);
                clearCanvas();
            }
        }

        function detectEdges(imageData) {
            const data = imageData.data;
            const width = imageData.width;
            const height = imageData.height;

            console.log('Starting comprehensive Canny-like edge detection...');

            // Convert to grayscale first
            const grayData = new Uint8Array(width * height);
            for (let i = 0; i < data.length; i += 4) {
                const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
                grayData[i / 4] = gray;
            }

            // Apply Gaussian blur to reduce noise
            const blurred = gaussianBlur(grayData, width, height, 1.0);

            // Calculate gradients using Sobel operators
            const gradients = sobelEdgeDetection(blurred, width, height);

            // Apply non-maximum suppression and double thresholding (Canny-like)
            const edges = cannyEdgeDetection(gradients, width, height, 10, 30); // lowThreshold: 10, highThreshold: 30

            // Extract edge points with better spacing for complete outline
            const edgePoints = [];
            const minDistance = 5; // Reduced for better coverage

            // First pass: collect all edge pixels
            const allEdgePixels = [];
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const idx = y * width + x;
                    if (edges[idx] > 0) {
                        allEdgePixels.push({ x, y, strength: edges[idx] });
                    }
                }
            }

            // Sort by edge strength to prioritize stronger edges
            allEdgePixels.sort((a, b) => b.strength - a.strength);

            // Second pass: select well-distributed edge points
            for (const pixel of allEdgePixels) {
                let farEnough = true;
                for (const existingPoint of edgePoints) {
                    const distance = Math.sqrt(
                        Math.pow(pixel.x - existingPoint.x, 2) +
                        Math.pow(pixel.y - existingPoint.y, 2)
                    );
                    if (distance < minDistance) {
                        farEnough = false;
                        break;
                    }
                }

                if (farEnough) {
                    edgePoints.push({ x: pixel.x, y: pixel.y });
                }
            }

            console.log('Comprehensive edge detection found', edgePoints.length, 'edge points');
            return edgePoints;
        }

        // Gaussian blur implementation
        function gaussianBlur(data, width, height, sigma) {
            const kernel = createGaussianKernel(sigma);
            const kernelSize = kernel.length;
            const radius = Math.floor(kernelSize / 2);
            const blurred = new Float32Array(width * height);

            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    let sum = 0;
                    let weightSum = 0;

                    for (let ky = -radius; ky <= radius; ky++) {
                        for (let kx = -radius; kx <= radius; kx++) {
                            const px = Math.max(0, Math.min(width - 1, x + kx));
                            const py = Math.max(0, Math.min(height - 1, y + ky));
                            const weight = kernel[ky + radius] * kernel[kx + radius];
                            sum += data[py * width + px] * weight;
                            weightSum += weight;
                        }
                    }

                    blurred[y * width + x] = sum / weightSum;
                }
            }

            return blurred;
        }

        // Create Gaussian kernel
        function createGaussianKernel(sigma) {
            const size = Math.ceil(sigma * 3) * 2 + 1;
            const kernel = new Float32Array(size);
            const center = Math.floor(size / 2);
            let sum = 0;

            for (let i = 0; i < size; i++) {
                const x = i - center;
                kernel[i] = Math.exp(-(x * x) / (2 * sigma * sigma));
                sum += kernel[i];
            }

            // Normalize
            for (let i = 0; i < size; i++) {
                kernel[i] /= sum;
            }

            return kernel;
        }

        // Sobel edge detection
        function sobelEdgeDetection(data, width, height) {
            const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
            const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

            const gradients = new Float32Array(width * height);
            const angles = new Float32Array(width * height);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let gx = 0, gy = 0;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const pixel = data[(y + ky) * width + (x + kx)];
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            gx += pixel * sobelX[kernelIdx];
                            gy += pixel * sobelY[kernelIdx];
                        }
                    }

                    const magnitude = Math.sqrt(gx * gx + gy * gy);
                    const angle = Math.atan2(gy, gx);

                    gradients[y * width + x] = magnitude;
                    angles[y * width + x] = angle;
                }
            }

            return { gradients, angles };
        }

        // Canny edge detection with non-maximum suppression and double thresholding
        function cannyEdgeDetection(gradientData, width, height, lowThreshold, highThreshold) {
            const { gradients, angles } = gradientData;
            const suppressed = new Float32Array(width * height);

            // Non-maximum suppression
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = y * width + x;
                    const angle = angles[idx];
                    const magnitude = gradients[idx];

                    // Determine the direction of the gradient
                    let neighbor1, neighbor2;
                    const angleRad = Math.abs(angle);

                    if (angleRad < Math.PI / 8 || angleRad > 7 * Math.PI / 8) {
                        // Horizontal edge
                        neighbor1 = gradients[y * width + (x - 1)];
                        neighbor2 = gradients[y * width + (x + 1)];
                    } else if (angleRad >= Math.PI / 8 && angleRad < 3 * Math.PI / 8) {
                        // Diagonal edge (/)
                        neighbor1 = gradients[(y - 1) * width + (x + 1)];
                        neighbor2 = gradients[(y + 1) * width + (x - 1)];
                    } else if (angleRad >= 3 * Math.PI / 8 && angleRad < 5 * Math.PI / 8) {
                        // Vertical edge
                        neighbor1 = gradients[(y - 1) * width + x];
                        neighbor2 = gradients[(y + 1) * width + x];
                    } else {
                        // Diagonal edge (\)
                        neighbor1 = gradients[(y - 1) * width + (x - 1)];
                        neighbor2 = gradients[(y + 1) * width + (x + 1)];
                    }

                    // Keep only local maxima
                    if (magnitude >= neighbor1 && magnitude >= neighbor2) {
                        suppressed[idx] = magnitude;
                    }
                }
            }

            // Double thresholding
            const edges = new Uint8Array(width * height);

            for (let i = 0; i < suppressed.length; i++) {
                if (suppressed[i] >= highThreshold) {
                    edges[i] = 255; // Strong edge
                } else if (suppressed[i] >= lowThreshold) {
                    edges[i] = 128; // Weak edge
                }
            }

            // Edge tracking by hysteresis
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = y * width + x;
                    if (edges[idx] === 128) { // Weak edge
                        // Check if connected to a strong edge
                        let connected = false;
                        for (let dy = -1; dy <= 1; dy++) {
                            for (let dx = -1; dx <= 1; dx++) {
                                if (edges[(y + dy) * width + (x + dx)] === 255) {
                                    connected = true;
                                    break;
                                }
                            }
                            if (connected) break;
                        }
                        edges[idx] = connected ? 255 : 0;
                    }
                }
            }

            return edges;
        }

        function smoothPoints(points, tension = 0.15) {
            if (points.length < 3) return points;
            
            const result = [];
            const size = points.length;
            
            for (let i = 0; i < size; i++) {
                const p0 = points[(i - 1 + size) % size];
                const p1 = points[i];
                const p2 = points[(i + 1) % size];
                const p3 = points[(i + 2) % size];
                
                // Catmull-Rom to Bezier conversion
                const cp1x = p1.x + (p2.x - p0.x) * tension;
                const cp1y = p1.y + (p2.y - p0.y) * tension;
                const cp2x = p2.x - (p3.x - p1.x) * tension;
                const cp2y = p2.y - (p3.y - p1.y) * tension;
                
                result.push({
                    point: p1,
                    cp1: { x: cp1x, y: cp1y },
                    cp2: { x: cp2x, y: cp2y }
                });
            }
            
            return result;
        }

        async function convertToDotToDot() {
            console.log('convertToDotToDot function started');
            console.log('Current points count:', points.length);
            console.log('Has original image data:', !!window.originalImageData);

            // If we have an uploaded image but no points, process the image first
            if (window.originalImageData && points.length < 2) {
                console.log('Processing uploaded image for edge detection');
                try {
                    updateStatus('Processing image with advanced edge detection...');

                    // Create a temporary canvas for processing
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = drawCanvas.width;
                    tempCanvas.height = drawCanvas.height;
                    const tempCtx = tempCanvas.getContext('2d');

                    // Put the original image data on temp canvas
                    tempCtx.putImageData(window.originalImageData, 0, 0);

                    // Convert canvas to image data URL
                    const imageDataUrl = tempCanvas.toDataURL();
                    console.log('Image data URL created, length:', imageDataUrl.length);

                    // Try to use image-js for advanced processing
                    let useAdvanced = false;
                    try {
                        if (typeof IJS !== 'undefined' && IJS.Image) {
                            console.log('Attempting to use image-js for advanced edge detection');

                            // Load image with image-js
                            const img = await IJS.Image.load(imageDataUrl);
                            console.log('Image loaded with image-js, size:', img.width, 'x', img.height);

                            // Convert to grayscale
                            const grey = img.grey();
                            console.log('Converted to grayscale');

                            // Apply Gaussian blur and edge detection
                            const blurred = grey.gaussianFilter({ sigma: 1.0 });
                            const edges = blurred.sobelFilter();
                            console.log('Applied Gaussian blur and Sobel filter');

                            // Apply threshold to get binary edge image
                            const threshold = 30;
                            const binaryEdges = edges.mask({ threshold });
                            console.log('Applied threshold mask');

                            // Extract edge points from the binary image
                            const edgePoints = [];
                            const minDistance = 10;

                            for (let y = 0; y < binaryEdges.height; y += 2) {
                                for (let x = 0; x < binaryEdges.width; x += 2) {
                                    if (binaryEdges.getBitXY(x, y)) {
                                        let farEnough = true;
                                        if (edgePoints.length > 0) {
                                            const lastEdge = edgePoints[edgePoints.length - 1];
                                            const distance = Math.sqrt(
                                                Math.pow(x - lastEdge.x, 2) +
                                                Math.pow(y - lastEdge.y, 2)
                                            );
                                            farEnough = distance >= minDistance;
                                        }

                                        if (farEnough) {
                                            edgePoints.push({ x, y });
                                        }
                                    }
                                }
                            }

                            console.log('Advanced edge detection found', edgePoints.length, 'edge points');
                            points = edgePoints;
                            useAdvanced = true;

                        } else {
                            console.log('image-js not available or not properly loaded');
                        }
                    } catch (advancedError) {
                        console.log('Error with advanced edge detection:', advancedError);
                        console.log('Falling back to simple edge detection');
                    }

                    // Fallback to simple edge detection if advanced method failed
                    if (!useAdvanced) {
                        console.log('Using simple edge detection fallback');
                        const processedImageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
                        const edges = detectEdges(processedImageData);
                        points = edges;
                        console.log('Simple edge detection found', points.length, 'edge points');
                    }

                    if (points.length === 0) {
                        console.log('No edges detected, using simple grid sampling');
                        // Fallback: create a simple grid of points
                        points = [];
                        const gridSize = 20;
                        for (let y = gridSize; y < drawCanvas.height; y += gridSize) {
                            for (let x = gridSize; x < drawCanvas.width; x += gridSize) {
                                const pixel = window.originalImageData.data;
                                const idx = (y * drawCanvas.width + x) * 4;
                                if (pixel[idx] < 200) { // If pixel is dark enough
                                    points.push({x, y});
                                }
                            }
                        }
                    }

                    updateStatus('Advanced edge detection complete. Creating dot-to-dot...');
                } catch (error) {
                    console.error('Error processing image:', error);
                    updateStatus('Error processing image. Using fallback method...', true);

                    // Fallback to simple edge detection
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = drawCanvas.width;
                    tempCanvas.height = drawCanvas.height;
                    const tempCtx = tempCanvas.getContext('2d');
                    tempCtx.putImageData(window.originalImageData, 0, 0);
                    const processedImageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
                    const edges = detectEdges(processedImageData);
                    points = edges.length > 0 ? edges : [];
                }
            }

            if (points.length < 2) {
                console.log('Not enough points for conversion');
                updateStatus('No points to convert. Please upload an image or draw something first.', true);
                return;
            }

            console.log('Proceeding with dot-to-dot conversion, points:', points.length);
            
            // Group nearby points to form continuous paths
            const paths = [];
            let currentPath = [points[0]];
            const maxGap = 40; // Maximum gap to connect points
            
            for (let i = 1; i < points.length; i++) {
                const point = points[i];
                const lastPoint = currentPath[currentPath.length - 1];
                
                const distance = Math.sqrt(
                    Math.pow(point.x - lastPoint.x, 2) + 
                    Math.pow(point.y - lastPoint.y, 2)
                );
                
                if (distance <= maxGap) {
                    currentPath.push(point);
                } else {
                    if (currentPath.length > 3) {
                        paths.push(currentPath);
                    }
                    currentPath = [point];
                }
            }
            
            if (currentPath.length > 3) {
                paths.push(currentPath);
            }
            
            // Sort paths by length, prioritize longer paths
            paths.sort((a, b) => b.length - a.length);
            
            // Combine paths that are likely part of the same outline
            const combinedPaths = [];
            const usedPaths = new Set();
            
            for (let i = 0; i < paths.length; i++) {
                if (usedPaths.has(i)) continue;
                
                let currentCombinedPath = [...paths[i]];
                usedPaths.add(i);
                
                let changed = true;
                while (changed) {
                    changed = false;
                    const start = currentCombinedPath[0];
                    const end = currentCombinedPath[currentCombinedPath.length - 1];
                    
                    for (let j = 0; j < paths.length; j++) {
                        if (usedPaths.has(j)) continue;
                        
                        const otherPath = paths[j];
                        const otherStart = otherPath[0];
                        const otherEnd = otherPath[otherPath.length - 1];
                        
                        // Check if paths can be connected
                        const startDist = Math.sqrt(
                            Math.pow(start.x - otherEnd.x, 2) + 
                            Math.pow(start.y - otherEnd.y, 2)
                        );
                        
                        const endDist = Math.sqrt(
                            Math.pow(end.x - otherStart.x, 2) + 
                            Math.pow(end.y - otherStart.y, 2)
                        );
                        
                        if (startDist <= maxGap) {
                            currentCombinedPath = [...otherPath, ...currentCombinedPath];
                            usedPaths.add(j);
                            changed = true;
                            break;
                        } else if (endDist <= maxGap) {
                            currentCombinedPath = [...currentCombinedPath, ...otherPath];
                            usedPaths.add(j);
                            changed = true;
                            break;
                        }
                    }
                }
                
                combinedPaths.push(currentCombinedPath);
            }
            
            // Update points with the combined paths
            points = combinedPaths.flatMap(path => path);

            const numDots = Math.max(5, Math.min(100, parseInt(dotCountInput.value) || 20));
            console.log('Target dot count:', numDots, 'Total points available:', points.length);
            const step = Math.max(1, Math.floor(points.length / numDots));
            const dots = points.filter((_, i) => i % step === 0).slice(0, numDots);
            const smoothedDots = smoothPoints(dots);

            // Clear dot canvas
            dotCtx.clearRect(0, 0, dotCanvas.width, dotCanvas.height);

            // Create SVG path for smoother curves
            let pathData = '';
            smoothedDots.forEach((segment, i) => {
                const { point, cp1, cp2 } = segment;
                const nextSegment = smoothedDots[(i + 1) % smoothedDots.length];
                
                if (i === 0) {
                    pathData += `M ${point.x} ${point.y} `;
                }
                
                pathData += `C ${cp1.x} ${cp1.y}, ${cp2.x} ${cp2.y}, ${nextSegment.point.x} ${nextSegment.point.y} `;
            });

            // Draw the vector path
            const path = new Path2D(pathData);
            dotCtx.save();
            dotCtx.strokeStyle = '#ccc';
            dotCtx.lineWidth = 1;
            dotCtx.setLineDash([5, 5]);
            dotCtx.stroke(path);
            dotCtx.setLineDash([]);
            dotCtx.restore();

            // Draw dots and numbers
            dotCtx.font = '16px Arial';
            dots.forEach((point, i) => {
                // Draw dot
                dotCtx.beginPath();
                dotCtx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                dotCtx.fillStyle = '#000';
                dotCtx.fill();

                // Draw number
                dotCtx.fillStyle = '#ff0000';
                dotCtx.fillText(i + 1, point.x + 8, point.y - 8);
            });

            updateStatus(`Dot-to-dot created with ${dots.length} numbered points!`);
            console.log('convertToDotToDot function completed successfully');
        }

        // Button event listeners
        convertBtn.addEventListener('click', async function() {
            console.log('🔥 Convert button clicked!');
            console.log('Current points:', points.length);
            console.log('Has original image data:', !!window.originalImageData);

            try {
                await convertToDotToDot();
                console.log('convertToDotToDot completed successfully');
            } catch (error) {
                console.error('Error in convertToDotToDot:', error);
                updateStatus('Error converting to dot-to-dot: ' + error.message, true);
            }
        });

        clearBtn.addEventListener('click', function() {
            console.log('Clear button clicked');
            clearCanvas();
        });

        // Check if image-js library is loaded
        window.addEventListener('load', function() {
            console.log('Page loaded, checking libraries...');
            console.log('IJS available:', typeof IJS !== 'undefined');
            console.log('convertBtn element:', convertBtn);

            if (typeof IJS !== 'undefined') {
                console.log('✅ image-js library loaded successfully');
                updateStatus('Ready! Advanced edge detection available.');
            } else {
                console.log('⚠️ image-js library not loaded, using fallback methods');
                updateStatus('Ready! Using basic edge detection.');
            }
        });

        // Initialize status
        updateStatus('Ready to upload image or draw manually');

        // Add some debugging info
        console.log('Dot-to-Dot Application Loaded Successfully!');
        console.log('Canvas dimensions:', drawCanvas.width, 'x', drawCanvas.height);
    </script>
</body>
</html>