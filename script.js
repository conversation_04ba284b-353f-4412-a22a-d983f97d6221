// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('newsletter-modal');
    const closeButton = document.querySelector('.close-button');
    const subscribeButton = document.getElementById('subscribe-button');
    const emailInput = document.getElementById('email-input');

    // Show modal after 3 seconds
    setTimeout(() => {
        modal.style.display = 'flex';
    }, 3000);

    // Close modal when clicking the close button
    closeButton.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Close modal when clicking outside the modal content
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Handle subscribe button click
    subscribeButton.addEventListener('click', () => {
        const email = emailInput.value.trim();
        if (validateEmail(email)) {
            // Here you would typically send the email to your server
            alert('Thank you for subscribing!');
            modal.style.display = 'none';
        } else {
            alert('Please enter a valid email address');
        }
    });

    // Email validation function
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // Handle 'Shop Now' button click
    const shopNowButton = document.querySelector('.cta-button');
    shopNowButton.addEventListener('click', () => {
        // Here you would typically redirect to the shop page
        alert('Redirecting to shop...');
    });

    // Add hover effect to navigation links
    const navLinks = document.querySelectorAll('.nav-links a');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', () => {
            link.style.textDecoration = 'underline';
        });
        link.addEventListener('mouseleave', () => {
            link.style.textDecoration = 'none';
        });
    });

    // Handle cart icon click
    const cartIcon = document.querySelector('.cart-icon');
    cartIcon.addEventListener('click', () => {
        alert('Shopping cart clicked!');
    });

    // Handle search
    const searchBox = document.querySelector('.search-box');
    searchBox.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            alert(`Searching for: ${searchBox.value}`);
        }
    });
});