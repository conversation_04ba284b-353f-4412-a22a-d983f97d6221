#!/usr/bin/env python3
"""
Simple HTTP server to serve the Document Chapter Extractor demo
This is a fallback solution while we troubleshoot the Java/Maven setup
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

PORT = 8080

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def open_browser():
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

def start_server():
    # Change to the document-chapter-extractor directory
    os.chdir('document-chapter-extractor')
    
    # Create a simple index.html that redirects to our demo
    with open('index.html', 'w') as f:
        f.write('''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Document Chapter Extractor</title>
    <meta http-equiv="refresh" content="0; url=demo.html">
</head>
<body>
    <p>Redirecting to Document Chapter Extractor...</p>
    <p>If you are not redirected, <a href="demo.html">click here</a>.</p>
</body>
</html>''')
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🌐 Document Chapter Extractor Demo Server")
        print(f"📍 Serving at: http://localhost:{PORT}")
        print(f"⏹️  Press Ctrl+C to stop")
        print()
        
        # Open browser in a separate thread
        threading.Thread(target=open_browser, daemon=True).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == "__main__":
    start_server()
