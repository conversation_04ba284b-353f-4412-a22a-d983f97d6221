<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <style>
        .upload-area {
            width: 300px;
            height: 200px;
            border: 2px dashed #ccc;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 20px;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .preview {
            max-width: 300px;
            max-height: 200px;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>Simple Upload Test</h1>
    
    <div class="upload-area" id="uploadArea">
        <div>
            <p>Click or drag image here</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
    </div>
    
    <div id="preview"></div>
    
    <script>
        console.log('Test script loading...');
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded');
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const preview = document.getElementById('preview');
            
            console.log('Upload area:', uploadArea);
            console.log('File input:', fileInput);
            
            // Click to upload
            uploadArea.addEventListener('click', () => {
                console.log('Upload area clicked');
                fileInput.click();
            });
            
            // File selection
            fileInput.addEventListener('change', (e) => {
                console.log('File selected:', e.target.files);
                const file = e.target.files[0];
                if (file) {
                    loadImage(file);
                }
            });
            
            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                console.log('Drag over');
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                console.log('Drag leave');
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                console.log('Drop event');
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                console.log('Dropped files:', files);
                if (files.length > 0) {
                    loadImage(files[0]);
                }
            });
            
            function loadImage(file) {
                console.log('Loading image:', file.name, file.type);
                
                if (!file.type.startsWith('image/')) {
                    alert('Please select an image file');
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    console.log('File read successfully');
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'preview';
                    img.onload = () => {
                        console.log('Image loaded:', img.width, 'x', img.height);
                    };
                    preview.innerHTML = '';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
            
            console.log('Event listeners set up');
        });
    </script>
</body>
</html>
