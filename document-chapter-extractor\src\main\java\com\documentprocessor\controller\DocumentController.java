package com.documentprocessor.controller;

import com.documentprocessor.model.Chapter;
import com.documentprocessor.model.DocumentProcessingResult;
import com.documentprocessor.service.DocumentProcessingService;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class DocumentController {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentController.class);
    
    @Autowired
    private DocumentProcessingService documentProcessingService;
    
    @PostMapping("/upload")
    public ResponseEntity<DocumentProcessingResult> uploadDocument(
            @RequestParam("file") MultipartFile file) {
        
        logger.info("Received file upload request: {}", file.getOriginalFilename());
        
        // Validate file
        if (file.isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(DocumentProcessingResult.error("File is empty"));
        }
        
        if (file.getSize() > 50 * 1024 * 1024) { // 50MB limit
            return ResponseEntity.badRequest()
                    .body(DocumentProcessingResult.error("File size exceeds 50MB limit"));
        }
        
        // Process document
        DocumentProcessingResult result = documentProcessingService.processDocument(file);
        
        if (result.isSuccess()) {
            logger.info("Document processed successfully: {} chapters extracted", 
                       result.getTotalChapters());
            return ResponseEntity.ok(result);
        } else {
            logger.error("Document processing failed: {}", result.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }
    
    @GetMapping("/download/{sessionId}/{chapterId}")
    public ResponseEntity<Resource> downloadChapter(
            @PathVariable String sessionId,
            @PathVariable String chapterId) {
        
        logger.info("Download request for session: {}, chapter: {}", sessionId, chapterId);
        
        Chapter chapter = documentProcessingService.getChapter(sessionId, chapterId);
        if (chapter == null) {
            return ResponseEntity.notFound().build();
        }
        
        try {
            // Create file content
            StringBuilder content = new StringBuilder();
            content.append(chapter.getTitle()).append("\n");
            content.append("=".repeat(chapter.getTitle().length())).append("\n\n");
            content.append("Word Count: ").append(chapter.getWordCount()).append("\n\n");
            content.append(chapter.getContent());
            
            byte[] fileContent = content.toString().getBytes(StandardCharsets.UTF_8);
            ByteArrayResource resource = new ByteArrayResource(fileContent);
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=\"" + chapter.getFileName() + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "text/plain; charset=UTF-8");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(fileContent.length)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Error creating download file: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/download-zip/{sessionId}")
    public ResponseEntity<Resource> downloadChaptersAsZip(
            @PathVariable String sessionId,
            @RequestParam(required = false) String ids) {
        
        logger.info("ZIP download request for session: {}, chapters: {}", sessionId, ids);
        
        List<Chapter> chapters;
        if (ids != null && !ids.trim().isEmpty()) {
            List<String> chapterIds = Arrays.asList(ids.split(","));
            chapters = documentProcessingService.getChapters(sessionId, chapterIds);
        } else {
            chapters = documentProcessingService.getAllChapters(sessionId);
        }
        
        if (chapters.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ZipOutputStream zos = new ZipOutputStream(baos);
            
            // Add each chapter as a file in the ZIP
            for (Chapter chapter : chapters) {
                StringBuilder content = new StringBuilder();
                content.append(chapter.getTitle()).append("\n");
                content.append("=".repeat(chapter.getTitle().length())).append("\n\n");
                content.append("Word Count: ").append(chapter.getWordCount()).append("\n\n");
                content.append(chapter.getContent());
                
                ZipEntry entry = new ZipEntry(chapter.getFileName());
                zos.putNextEntry(entry);
                zos.write(content.toString().getBytes(StandardCharsets.UTF_8));
                zos.closeEntry();
            }
            
            // Add table of contents
            StringBuilder toc = new StringBuilder();
            toc.append("TABLE OF CONTENTS\n");
            toc.append("=".repeat(17)).append("\n\n");
            for (Chapter chapter : chapters) {
                toc.append(String.format("%d. %s (%d words)\n", 
                          chapter.getChapterNumber(), chapter.getTitle(), chapter.getWordCount()));
            }
            
            ZipEntry tocEntry = new ZipEntry("00_Table_of_Contents.txt");
            zos.putNextEntry(tocEntry);
            zos.write(toc.toString().getBytes(StandardCharsets.UTF_8));
            zos.closeEntry();
            
            zos.close();
            
            byte[] zipContent = baos.toByteArray();
            ByteArrayResource resource = new ByteArrayResource(zipContent);
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=\"chapters.zip\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(zipContent.length)
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("Error creating ZIP file: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/session/{sessionId}")
    public ResponseEntity<Void> cleanupSession(@PathVariable String sessionId) {
        documentProcessingService.cleanupSession(sessionId);
        return ResponseEntity.ok().build();
    }
}
