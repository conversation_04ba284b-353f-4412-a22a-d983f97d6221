<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dot-to-Dot Image Editor</title>
    <link rel="stylesheet" href="dot-to-dot-editor.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1><i class="fas fa-circle-dot"></i> Dot-to-Dot Image Editor</h1>
            <p>Upload an image, generate dots, and create professional dot-to-dot puzzles</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Panel - Controls -->
            <div class="left-panel">
                <!-- Upload Section -->
                <section class="upload-section">
                    <h3><i class="fas fa-upload"></i> Upload Image</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop your image here</p>
                            <p class="upload-subtitle">or click to browse</p>
                            <input type="file" id="fileInput" accept="image/*" hidden>
                        </div>
                        <div class="upload-preview" id="uploadPreview" style="display: none;">
                            <img id="previewImage" alt="Preview">
                            <button class="remove-image" id="removeImage">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="supported-formats">
                        <small>Supported: JPG, PNG, GIF, BMP</small>
                    </div>
                </section>

                <!-- Controls Section -->
                <section class="controls-section">
                    <h3><i class="fas fa-sliders-h"></i> Dot Settings</h3>
                    
                    <!-- Dot Count -->
                    <div class="control-group">
                        <label for="dotCount">Number of Dots: <span id="dotCountValue">50</span></label>
                        <input type="range" id="dotCount" min="10" max="300" value="50" class="slider">
                    </div>

                    <!-- Edge Sensitivity -->
                    <div class="control-group">
                        <label for="edgeSensitivity">Edge Sensitivity: <span id="edgeSensitivityValue">50</span></label>
                        <input type="range" id="edgeSensitivity" min="10" max="100" value="50" class="slider">
                    </div>

                    <!-- Toggle Options -->
                    <div class="toggle-group">
                        <div class="toggle-item">
                            <input type="checkbox" id="showConnections" checked>
                            <label for="showConnections">Show connecting lines</label>
                        </div>
                        <div class="toggle-item">
                            <input type="checkbox" id="snapToOutline">
                            <label for="snapToOutline">Snap to outline</label>
                        </div>
                        <div class="toggle-item">
                            <input type="checkbox" id="showBackground" checked>
                            <label for="showBackground">Show background image</label>
                        </div>
                    </div>
                </section>

                <!-- Action Buttons -->
                <section class="actions-section">
                    <h3><i class="fas fa-tools"></i> Actions</h3>
                    <div class="button-grid">
                        <button class="btn btn-primary" id="generateBtn">
                            <i class="fas fa-magic"></i> Generate Dot-to-Dot
                        </button>
                        <button class="btn btn-secondary" id="evenSpaceBtn" disabled>
                            <i class="fas fa-expand-arrows-alt"></i> Evenly Space Dots
                        </button>
                        <button class="btn btn-secondary" id="autoRenumberBtn" disabled>
                            <i class="fas fa-sort-numeric-down"></i> Auto Renumber
                        </button>
                        <button class="btn btn-danger" id="clearAllBtn" disabled>
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>
                </section>

                <!-- Export Section -->
                <section class="export-section">
                    <h3><i class="fas fa-download"></i> Export</h3>
                    <div class="export-buttons">
                        <button class="btn btn-export" id="exportPNG" disabled>
                            <i class="fas fa-file-image"></i> PNG
                        </button>
                        <button class="btn btn-export" id="exportJPEG" disabled>
                            <i class="fas fa-file-image"></i> JPEG
                        </button>
                        <button class="btn btn-export" id="exportSVG" disabled>
                            <i class="fas fa-file-code"></i> SVG
                        </button>
                    </div>
                </section>

                <!-- Status Section -->
                <section class="status-section">
                    <div class="status-message" id="statusMessage"></div>
                    <div class="progress-bar" id="progressBar" style="display: none;">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </section>
            </div>

            <!-- Right Panel - Canvas -->
            <div class="right-panel">
                <div class="canvas-container">
                    <!-- Canvas Controls -->
                    <div class="canvas-controls">
                        <div class="zoom-controls">
                            <button class="btn btn-icon" id="zoomOut">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span class="zoom-level" id="zoomLevel">100%</span>
                            <button class="btn btn-icon" id="zoomIn">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button class="btn btn-icon" id="resetZoom">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                        <div class="canvas-info">
                            <span id="canvasInfo">Ready to upload image</span>
                        </div>
                        <div class="undo-redo">
                            <button class="btn btn-icon" id="undoBtn" disabled>
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="btn btn-icon" id="redoBtn" disabled>
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Main Canvas -->
                    <div class="canvas-wrapper" id="canvasWrapper">
                        <canvas id="backgroundCanvas" class="background-canvas"></canvas>
                        <canvas id="dotsCanvas" class="dots-canvas"></canvas>
                        <div class="canvas-placeholder" id="canvasPlaceholder">
                            <i class="fas fa-image"></i>
                            <p>Upload an image to start creating your dot-to-dot</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <p>Processing your image...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="dot-to-dot-editor.js?v=3"></script>
</body>
</html>
