@echo off
echo.
echo ========================================
echo   Document Chapter Extractor Launcher
echo ========================================
echo.

REM Set Java path directly
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set JAVA_BIN=%JAVA_HOME%\bin\java.exe
set PATH=%JAVA_HOME%\bin;%PATH%

echo ✅ Using Java at: %JAVA_HOME%

REM Test Java
echo 🧪 Testing Java...
"%JAVA_BIN%" -version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java test failed
    pause
    exit /b 1
)

echo ✅ Java is working!

REM Set Maven path
set MAVEN_HOME=C:\Users\<USER>\maven
set MAVEN_BIN=%MAVEN_HOME%\mvn\bin\mvn.cmd
set PATH=%MAVEN_HOME%\mvn\bin;%PATH%

echo 🧪 Testing Maven...
"%MAVEN_BIN%" --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Maven test failed
    pause
    exit /b 1
)

echo ✅ Maven is working!
echo.

REM Navigate to project
cd document-chapter-extractor
echo 📁 Changed to project directory

echo.
echo 🔨 Building application...
echo    This may take a few minutes for the first build...
echo.

"%MAVEN_BIN%" clean install

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 🚀 Starting Document Chapter Extractor...
echo.
echo 📍 Application will be available at: http://localhost:8080
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start the application
"%MAVEN_BIN%" spring-boot:run
