<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dot-to-Dot Image Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .left-panel {
            padding: 30px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .right-panel {
            padding: 30px;
            background: white;
        }

        .upload-area {
            border: 3px dashed #4ecdc4;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #ff6b6b;
            background: rgba(78, 205, 196, 0.05);
        }

        .upload-area.dragover {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
        }

        .upload-icon {
            font-size: 3em;
            color: #4ecdc4;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .preview-container {
            margin: 20px 0;
            text-align: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4ecdc4;
            cursor: pointer;
        }

        .slider-value {
            font-weight: bold;
            color: #4ecdc4;
            min-width: 40px;
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .toggle {
            position: relative;
            width: 50px;
            height: 25px;
            background: #ddd;
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle.active {
            background: #4ecdc4;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle.active .toggle-slider {
            transform: translateX(25px);
        }

        .generate-btn {
            width: 100%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-top: 20px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
        }

        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .output-container {
            text-align: center;
        }

        .output-canvas {
            max-width: 100%;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .download-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .download-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .download-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .download-btn:hover {
            transform: translateY(-2px);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .left-panel, .right-panel {
                padding: 20px;
            }
        }

        .hidden {
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Dot-to-Dot Converter</h1>
            <p>Transform any image into a fun dot-to-dot drawing!</p>
        </div>

        <div class="main-content">
            <!-- Left Panel: Upload & Controls -->
            <div class="left-panel">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Drag & drop an image here</div>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose Image
                    </button>
                    <input type="file" id="fileInput" class="file-input" accept="image/*">
                </div>

                <div class="preview-container" id="previewContainer" style="display: none;">
                    <img id="previewImage" class="preview-image" alt="Preview">
                </div>

                <div class="controls">
                    <div class="control-group">
                        <label class="control-label">Number of Dots:</label>
                        <div class="slider-container">
                            <input type="range" id="dotCount" class="slider" min="10" max="200" value="50">
                            <span class="slider-value" id="dotCountValue">50</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Connect the Dots:</label>
                        <div class="toggle-container">
                            <div class="toggle active" id="connectToggle">
                                <div class="toggle-slider"></div>
                            </div>
                            <span>Show connecting lines</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Edge Sensitivity:</label>
                        <div class="slider-container">
                            <input type="range" id="edgeSensitivity" class="slider" min="10" max="100" value="30">
                            <span class="slider-value" id="edgeSensitivityValue">30</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Dot Spacing:</label>
                        <div class="toggle-container">
                            <div class="toggle active" id="evenSpacingToggle">
                                <div class="toggle-slider"></div>
                            </div>
                            <span>Even spacing along edges</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label">Background Image:</label>
                        <div class="toggle-container">
                            <div class="toggle active" id="backgroundToggle">
                                <div class="toggle-slider"></div>
                            </div>
                            <span>Show background for editing</span>
                        </div>
                    </div>

                    <button class="generate-btn" id="generateBtn" disabled>
                        <span id="generateText">Generate Dot-to-Dot</span>
                        <span id="loadingSpinner" class="loading hidden"></span>
                    </button>

                    <div class="backend-status" id="backendStatus" style="margin-top: 15px; padding: 10px; border-radius: 8px; text-align: center; font-size: 0.9em; display: none;">
                        <span id="backendStatusText"></span>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Output -->
            <div class="right-panel">
                <div class="output-container">
                    <h3>Dot-to-Dot Result</h3>
                    <canvas id="outputCanvas" class="output-canvas" width="500" height="500"></canvas>
                    
                    <div id="statusMessage" class="status hidden"></div>

                    <div class="download-section" id="downloadSection" style="display: none;">
                        <h4>Download Your Dot-to-Dot</h4>
                        <div class="download-buttons">
                            <button class="download-btn" onclick="downloadImage('png')">Download PNG</button>
                            <button class="download-btn" onclick="downloadImage('jpeg')">Download JPEG</button>
                            <button class="download-btn" onclick="downloadImage('svg')">Download SVG</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/image-js@0.35.6/dist/image.js"></script>
    <script>
        // Global variables
        let uploadedImage = null;
        let currentImageData = null;
        let dotToDotsData = null;
        let backendAvailable = false;
        const BACKEND_URL = 'http://localhost:5000';

        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const previewImage = document.getElementById('previewImage');
        const generateBtn = document.getElementById('generateBtn');
        const outputCanvas = document.getElementById('outputCanvas');
        const outputCtx = outputCanvas.getContext('2d');
        const dotCountSlider = document.getElementById('dotCount');
        const dotCountValue = document.getElementById('dotCountValue');
        const edgeSensitivitySlider = document.getElementById('edgeSensitivity');
        const edgeSensitivityValue = document.getElementById('edgeSensitivityValue');
        const connectToggle = document.getElementById('connectToggle');
        const evenSpacingToggle = document.getElementById('evenSpacingToggle');
        const backgroundToggle = document.getElementById('backgroundToggle');
        const statusMessage = document.getElementById('statusMessage');
        const downloadSection = document.getElementById('downloadSection');
        const generateText = document.getElementById('generateText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        // Initialize the application
        function init() {
            setupEventListeners();
            updateSliderValues();
            checkBackendAvailability();
            showStatus('Upload an image to get started!', 'info');
        }

        // Check if backend server is available
        async function checkBackendAvailability() {
            const backendStatus = document.getElementById('backendStatus');
            const backendStatusText = document.getElementById('backendStatusText');

            try {
                const response = await fetch(BACKEND_URL, {
                    method: 'GET',
                    mode: 'cors'
                });
                if (response.ok) {
                    backendAvailable = true;
                    console.log('✅ Backend server detected - Enhanced processing available');

                    // Show backend status in UI
                    backendStatus.style.display = 'block';
                    backendStatus.style.background = '#d4edda';
                    backendStatus.style.color = '#155724';
                    backendStatus.style.border = '1px solid #c3e6cb';
                    backendStatusText.textContent = '🚀 Enhanced Backend Processing Available';

                    showStatus('Backend server connected - Enhanced processing available!', 'success');
                    setTimeout(() => showStatus('Upload an image to get started!', 'info'), 3000);
                }
            } catch (error) {
                backendAvailable = false;
                console.log('ℹ️ Backend server not available - Using client-side processing');

                // Show fallback status in UI
                backendStatus.style.display = 'block';
                backendStatus.style.background = '#d1ecf1';
                backendStatus.style.color = '#0c5460';
                backendStatus.style.border = '1px solid #bee5eb';
                backendStatusText.textContent = '💻 Client-Side Processing Mode';
            }
        }

        // Setup all event listeners
        function setupEventListeners() {
            // File upload events
            fileInput.addEventListener('change', handleFileSelect);
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Control events
            dotCountSlider.addEventListener('input', updateSliderValues);
            edgeSensitivitySlider.addEventListener('input', updateSliderValues);
            connectToggle.addEventListener('click', toggleConnect);
            evenSpacingToggle.addEventListener('click', toggleEvenSpacing);
            backgroundToggle.addEventListener('click', toggleBackgroundImage);
            generateBtn.addEventListener('click', generateDotToDot);

            // Add drag and drop functionality to output canvas
            outputCanvas.addEventListener('mousedown', handleMouseDown);
            outputCanvas.addEventListener('mousemove', handleMouseMove);
            outputCanvas.addEventListener('mouseup', handleMouseUp);
            outputCanvas.addEventListener('mouseleave', handleMouseUp);
        }

        // Handle file selection
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processImageFile(file);
            }
        }

        // Handle drag and drop
        function handleDragOver(event) {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processImageFile(files[0]);
            }
        }

        // Process uploaded image file
        function processImageFile(file) {
            if (!file.type.startsWith('image/')) {
                showStatus('Please select a valid image file.', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                uploadedImage = new Image();
                uploadedImage.onload = function() {
                    previewImage.src = e.target.result;
                    previewContainer.style.display = 'block';
                    generateBtn.disabled = false;
                    showStatus('Image loaded successfully! Click "Generate Dot-to-Dot" to convert.', 'success');

                    // Store image data for processing
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = this.width;
                    canvas.height = this.height;
                    ctx.drawImage(this, 0, 0);
                    currentImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                };
                uploadedImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        // Update slider values display
        function updateSliderValues() {
            dotCountValue.textContent = dotCountSlider.value;
            edgeSensitivityValue.textContent = edgeSensitivitySlider.value;
        }

        // Toggle connect dots feature
        function toggleConnect() {
            connectToggle.classList.toggle('active');
        }

        // Toggle even spacing feature
        function toggleEvenSpacing() {
            evenSpacingToggle.classList.toggle('active');
        }

        // Toggle background image feature
        function toggleBackgroundImage() {
            backgroundToggle.classList.toggle('active');
            showBackgroundImage = backgroundToggle.classList.contains('active');

            // Redraw if we have dots
            if (currentDots.length > 0) {
                const showConnections = connectToggle.classList.contains('active');
                drawDotToDot(currentDots, showConnections, showBackgroundImage);
            }
        }

        // Show status message
        function showStatus(message, type) {
            statusMessage.textContent = message;
            statusMessage.className = `status ${type}`;
            statusMessage.classList.remove('hidden');
        }

        // Hide status message
        function hideStatus() {
            statusMessage.classList.add('hidden');
        }

        // Main dot-to-dot generation function
        async function generateDotToDot() {
            if (!currentImageData) {
                showStatus('Please upload an image first.', 'error');
                return;
            }

            // Get current settings and log them
            const dotCount = parseInt(dotCountSlider.value);
            const edgeSensitivity = parseInt(edgeSensitivitySlider.value);
            const showConnections = connectToggle.classList.contains('active');
            const useEvenSpacing = evenSpacingToggle.classList.contains('active');

            console.log(`🎯 STARTING DOT-TO-DOT GENERATION:`, {
                targetDotCount: dotCount,
                edgeSensitivity,
                showConnections,
                useEvenSpacing,
                backendAvailable
            });

            // Show loading state
            generateBtn.disabled = true;
            generateText.classList.add('hidden');
            loadingSpinner.classList.remove('hidden');
            showStatus('Processing image and detecting edges...', 'info');

            try {
                // Get current settings
                const dotCount = parseInt(dotCountSlider.value);
                const edgeSensitivity = parseInt(edgeSensitivitySlider.value);
                const showConnections = connectToggle.classList.contains('active');
                const useEvenSpacing = evenSpacingToggle.classList.contains('active');

                console.log(`🎯 Generation Parameters:`, {
                    dotCount,
                    edgeSensitivity,
                    showConnections,
                    useEvenSpacing,
                    backendAvailable
                });

                // Process the image - use backend if available for enhanced processing
                let orderedDots;
                if (backendAvailable && useEvenSpacing) {
                    showStatus('Using backend server for enhanced processing...', 'info');
                    orderedDots = await processWithBackend(dotCount, edgeSensitivity, showConnections);
                } else {
                    showStatus('Processing image with client-side algorithms...', 'info');
                    const edgePoints = await detectEdges(currentImageData, edgeSensitivity);
                    const selectedDots = useEvenSpacing ?
                        selectOptimalDots(edgePoints, dotCount) :
                        selectOptimalDotsLegacy(edgePoints, dotCount);
                    orderedDots = orderDotsForDrawing(selectedDots);
                }

                // Draw the dot-to-dot with background enabled by default
                const showBackground = backgroundToggle.classList.contains('active');
                drawDotToDot(orderedDots, showConnections, showBackground);

                // Store data for download
                dotToDotsData = {
                    dots: orderedDots,
                    showConnections: showConnections,
                    originalWidth: currentImageData.width,
                    originalHeight: currentImageData.height
                };

                // Show success and enable downloads
                showStatus(`Dot-to-dot created with ${orderedDots.length} dots!`, 'success');
                downloadSection.style.display = 'block';

            } catch (error) {
                console.error('Error generating dot-to-dot:', error);
                showStatus('Error processing image. Please try again.', 'error');
            } finally {
                // Reset loading state
                generateBtn.disabled = false;
                generateText.classList.remove('hidden');
                loadingSpinner.classList.add('hidden');
            }
        }

        // Process image using backend server
        async function processWithBackend(dotCount, edgeSensitivity, showConnections) {
            try {
                // Convert current image to base64
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = currentImageData.width;
                canvas.height = currentImageData.height;

                // Put image data on canvas
                ctx.putImageData(currentImageData, 0, 0);

                // Convert to base64
                const base64Image = canvas.toDataURL('image/png');

                // Send to backend
                const response = await fetch(`${BACKEND_URL}/process`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image: base64Image,
                        dotCount: dotCount,
                        edgeSensitivity: edgeSensitivity,
                        showConnections: showConnections
                    })
                });

                if (!response.ok) {
                    throw new Error(`Backend error: ${response.status}`);
                }

                const result = await response.json();

                if (result.status === 'success') {
                    // Convert backend points to our format
                    const backendDots = result.points.map(point => ({
                        x: point[0],
                        y: point[1],
                        strength: 1
                    }));
                    console.log(`🔧 Backend returned ${backendDots.length} dots (requested: ${dotCount})`);
                    return backendDots;
                } else {
                    throw new Error(result.error || 'Backend processing failed');
                }

            } catch (error) {
                console.error('Backend processing failed:', error);
                showStatus('Backend processing failed, falling back to client-side...', 'error');

                // Fallback to client-side processing
                const edgePoints = await detectEdges(currentImageData, edgeSensitivity);
                const selectedDots = selectOptimalDots(edgePoints, dotCount);
                return orderDotsForDrawing(selectedDots);
            }
        }

        // Enhanced edge detection with multiple algorithms for detailed results
        async function detectEdges(imageData, sensitivity) {
            const { data, width, height } = imageData;

            // Convert to grayscale with better contrast
            const grayData = new Uint8Array(width * height);
            for (let i = 0; i < data.length; i += 4) {
                // Use luminance formula for better grayscale conversion
                const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
                grayData[i / 4] = gray;
            }

            // Apply multiple edge detection techniques for more detailed results
            const edges1 = await cannyEdgeDetection(grayData, width, height, sensitivity);
            const edges2 = await laplacianEdgeDetection(grayData, width, height, sensitivity);
            const edges3 = await robertsEdgeDetection(grayData, width, height, sensitivity);

            // Combine edge detection results for maximum detail
            const combinedEdges = new Uint8Array(width * height);
            for (let i = 0; i < combinedEdges.length; i++) {
                combinedEdges[i] = Math.max(edges1[i], edges2[i], edges3[i]);
            }

            // Apply morphological operations to enhance edges
            const enhancedEdges = morphologicalEnhancement(combinedEdges, width, height);

            // Extract edge points with strength information
            const edgePoints = [];
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = y * width + x;
                    if (enhancedEdges[idx] > 0) {
                        // Calculate local edge strength
                        const strength = calculateLocalEdgeStrength(enhancedEdges, x, y, width, height);
                        edgePoints.push({
                            x: x,
                            y: y,
                            strength: strength
                        });
                    }
                }
            }

            console.log(`Detected ${edgePoints.length} edge points`);
            return edgePoints;
        }

        // Enhanced Canny edge detection
        async function cannyEdgeDetection(grayData, width, height, sensitivity) {
            // Apply stronger Gaussian blur for noise reduction
            const sigma = 1.4; // Increased for better noise reduction
            const blurred = gaussianBlur(grayData, width, height, sigma);

            // Calculate gradients using Sobel operators
            const gradients = sobelEdgeDetection(blurred, width, height);

            // Apply non-maximum suppression
            const suppressed = nonMaximumSuppression(gradients.magnitudes, gradients.angles, width, height);

            // Double thresholding with sensitivity-based values
            const lowThreshold = Math.max(5, (100 - sensitivity) * 1.5);
            const highThreshold = lowThreshold * 2.5;

            return doubleThresholding(suppressed, width, height, lowThreshold, highThreshold);
        }

        // Laplacian edge detection for fine details
        async function laplacianEdgeDetection(grayData, width, height, sensitivity) {
            const laplacianKernel = [
                0, -1, 0,
                -1, 4, -1,
                0, -1, 0
            ];

            const result = new Float32Array(width * height);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let sum = 0;
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const pixel = grayData[(y + ky) * width + (x + kx)];
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            sum += pixel * laplacianKernel[kernelIdx];
                        }
                    }
                    result[y * width + x] = Math.abs(sum);
                }
            }

            // Apply threshold based on sensitivity
            const threshold = (100 - sensitivity) * 3;
            const edges = new Uint8Array(width * height);
            for (let i = 0; i < result.length; i++) {
                edges[i] = result[i] > threshold ? 255 : 0;
            }

            return edges;
        }

        // Roberts cross-gradient edge detection
        async function robertsEdgeDetection(grayData, width, height, sensitivity) {
            const result = new Float32Array(width * height);

            for (let y = 0; y < height - 1; y++) {
                for (let x = 0; x < width - 1; x++) {
                    const p1 = grayData[y * width + x];
                    const p2 = grayData[y * width + (x + 1)];
                    const p3 = grayData[(y + 1) * width + x];
                    const p4 = grayData[(y + 1) * width + (x + 1)];

                    const gx = p1 - p4;
                    const gy = p2 - p3;

                    result[y * width + x] = Math.sqrt(gx * gx + gy * gy);
                }
            }

            // Apply threshold
            const threshold = (100 - sensitivity) * 2;
            const edges = new Uint8Array(width * height);
            for (let i = 0; i < result.length; i++) {
                edges[i] = result[i] > threshold ? 255 : 0;
            }

            return edges;
        }

        // Non-maximum suppression for Canny edge detection
        function nonMaximumSuppression(magnitudes, angles, width, height) {
            const suppressed = new Float32Array(width * height);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = y * width + x;
                    const magnitude = magnitudes[idx];
                    const angle = angles[idx];

                    // Convert angle to degrees and normalize
                    let angleDeg = (angle * 180 / Math.PI + 180) % 180;

                    let neighbor1, neighbor2;

                    // Determine neighbors based on gradient direction
                    if (angleDeg < 22.5 || angleDeg >= 157.5) {
                        // Horizontal edge
                        neighbor1 = magnitudes[y * width + (x - 1)];
                        neighbor2 = magnitudes[y * width + (x + 1)];
                    } else if (angleDeg >= 22.5 && angleDeg < 67.5) {
                        // Diagonal edge (/)
                        neighbor1 = magnitudes[(y - 1) * width + (x + 1)];
                        neighbor2 = magnitudes[(y + 1) * width + (x - 1)];
                    } else if (angleDeg >= 67.5 && angleDeg < 112.5) {
                        // Vertical edge
                        neighbor1 = magnitudes[(y - 1) * width + x];
                        neighbor2 = magnitudes[(y + 1) * width + x];
                    } else {
                        // Diagonal edge (\)
                        neighbor1 = magnitudes[(y - 1) * width + (x - 1)];
                        neighbor2 = magnitudes[(y + 1) * width + (x + 1)];
                    }

                    // Suppress if not local maximum
                    if (magnitude >= neighbor1 && magnitude >= neighbor2) {
                        suppressed[idx] = magnitude;
                    } else {
                        suppressed[idx] = 0;
                    }
                }
            }

            return suppressed;
        }

        // Double thresholding with hysteresis
        function doubleThresholding(suppressed, width, height, lowThreshold, highThreshold) {
            const edges = new Uint8Array(width * height);
            const strong = 255;
            const weak = 128;

            // Apply thresholds
            for (let i = 0; i < suppressed.length; i++) {
                if (suppressed[i] >= highThreshold) {
                    edges[i] = strong;
                } else if (suppressed[i] >= lowThreshold) {
                    edges[i] = weak;
                } else {
                    edges[i] = 0;
                }
            }

            // Edge tracking by hysteresis
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = y * width + x;
                    if (edges[idx] === weak) {
                        // Check if connected to strong edge
                        let hasStrongNeighbor = false;
                        for (let dy = -1; dy <= 1; dy++) {
                            for (let dx = -1; dx <= 1; dx++) {
                                if (dx === 0 && dy === 0) continue;
                                const neighborIdx = (y + dy) * width + (x + dx);
                                if (edges[neighborIdx] === strong) {
                                    hasStrongNeighbor = true;
                                    break;
                                }
                            }
                            if (hasStrongNeighbor) break;
                        }

                        edges[idx] = hasStrongNeighbor ? strong : 0;
                    }
                }
            }

            return edges;
        }

        // Morphological enhancement to strengthen edges
        function morphologicalEnhancement(edges, width, height) {
            const enhanced = new Uint8Array(width * height);

            // Dilation followed by erosion (closing operation)
            const dilated = new Uint8Array(width * height);

            // Dilation
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let maxVal = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const val = edges[(y + dy) * width + (x + dx)];
                            maxVal = Math.max(maxVal, val);
                        }
                    }
                    dilated[y * width + x] = maxVal;
                }
            }

            // Erosion
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let minVal = 255;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const val = dilated[(y + dy) * width + (x + dx)];
                            minVal = Math.min(minVal, val);
                        }
                    }
                    enhanced[y * width + x] = minVal;
                }
            }

            return enhanced;
        }

        // Calculate local edge strength
        function calculateLocalEdgeStrength(edges, x, y, width, height) {
            let strength = 0;
            let count = 0;

            for (let dy = -2; dy <= 2; dy++) {
                for (let dx = -2; dx <= 2; dx++) {
                    const nx = x + dx;
                    const ny = y + dy;
                    if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                        strength += edges[ny * width + nx];
                        count++;
                    }
                }
            }

            return count > 0 ? strength / count : 0;
        }

        // Gaussian blur implementation
        function gaussianBlur(data, width, height, sigma) {
            const kernel = createGaussianKernel(sigma);
            const kernelSize = kernel.length;
            const radius = Math.floor(kernelSize / 2);
            const blurred = new Float32Array(width * height);

            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    let sum = 0;
                    let weightSum = 0;

                    for (let ky = -radius; ky <= radius; ky++) {
                        for (let kx = -radius; kx <= radius; kx++) {
                            const px = Math.max(0, Math.min(width - 1, x + kx));
                            const py = Math.max(0, Math.min(height - 1, y + ky));
                            const weight = kernel[ky + radius] * kernel[kx + radius];
                            sum += data[py * width + px] * weight;
                            weightSum += weight;
                        }
                    }

                    blurred[y * width + x] = sum / weightSum;
                }
            }

            return blurred;
        }

        // Create Gaussian kernel
        function createGaussianKernel(sigma) {
            const size = Math.ceil(sigma * 3) * 2 + 1;
            const kernel = new Float32Array(size);
            const center = Math.floor(size / 2);
            let sum = 0;

            for (let i = 0; i < size; i++) {
                const x = i - center;
                kernel[i] = Math.exp(-(x * x) / (2 * sigma * sigma));
                sum += kernel[i];
            }

            // Normalize
            for (let i = 0; i < size; i++) {
                kernel[i] /= sum;
            }

            return kernel;
        }

        // Sobel edge detection
        function sobelEdgeDetection(data, width, height) {
            const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
            const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

            const magnitudes = new Float32Array(width * height);
            const angles = new Float32Array(width * height);

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let gx = 0, gy = 0;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const pixel = data[(y + ky) * width + (x + kx)];
                            const kernelIdx = (ky + 1) * 3 + (kx + 1);
                            gx += pixel * sobelX[kernelIdx];
                            gy += pixel * sobelY[kernelIdx];
                        }
                    }

                    const magnitude = Math.sqrt(gx * gx + gy * gy);
                    const angle = Math.atan2(gy, gx);

                    magnitudes[y * width + x] = magnitude;
                    angles[y * width + x] = angle;
                }
            }

            return { magnitudes, angles };
        }

        // Apply threshold to edge magnitudes
        function applyThreshold(magnitudes, threshold) {
            const result = new Uint8Array(magnitudes.length);
            for (let i = 0; i < magnitudes.length; i++) {
                result[i] = magnitudes[i] > threshold ? 255 : 0;
            }
            return result;
        }

        // Select optimal dots with even spacing along detected edges
        function selectOptimalDots(edgePoints, targetCount) {
            if (edgePoints.length === 0) return [];

            console.log(`Selecting ${targetCount} dots from ${edgePoints.length} edge points`);

            // If we have fewer edge points than target, return all
            if (edgePoints.length <= targetCount) {
                console.log(`Using all ${edgePoints.length} edge points`);
                return edgePoints;
            }

            // Organize edge points into connected contours
            const contours = organizeIntoContours(edgePoints);
            console.log(`Organized into ${contours.length} contours`);

            // Filter out very small contours (noise)
            const significantContours = contours.filter(contour => contour.length >= 3);
            console.log(`${significantContours.length} significant contours after filtering`);

            if (significantContours.length === 0) {
                // Fallback: if no contours found, use simple grid-based selection
                console.log('No significant contours found, using grid-based fallback');
                return selectDotsGridBased(edgePoints, targetCount);
            }

            // Calculate total perimeter of all significant contours
            let totalPerimeter = 0;
            const contourLengths = significantContours.map(contour => {
                const length = calculateContourLength(contour);
                totalPerimeter += length;
                return length;
            });

            // Distribute dots proportionally among contours
            const selectedDots = [];
            let remainingDots = targetCount;

            for (let i = 0; i < significantContours.length; i++) {
                const contour = significantContours[i];
                const contourLength = contourLengths[i];

                // Calculate number of dots for this contour (proportional to length)
                let dotsForContour;
                if (i === significantContours.length - 1) {
                    // Last contour gets all remaining dots
                    dotsForContour = remainingDots;
                } else {
                    dotsForContour = Math.max(1, Math.round((contourLength / totalPerimeter) * targetCount));
                    dotsForContour = Math.min(dotsForContour, remainingDots);
                }

                // Select evenly spaced points along this contour
                const contourDots = selectEvenlySpacedPoints(contour, dotsForContour);
                selectedDots.push(...contourDots);
                remainingDots -= contourDots.length;

                if (remainingDots <= 0) break;
            }

            // Ensure we have exactly the target count
            if (selectedDots.length > targetCount) {
                // Sort by strength and keep the strongest
                selectedDots.sort((a, b) => b.strength - a.strength);
                selectedDots.length = targetCount;
            } else if (selectedDots.length < targetCount) {
                console.log(`Need ${targetCount - selectedDots.length} more dots`);
                // Add more dots using distance-based selection
                const additionalDots = selectAdditionalDots(edgePoints, selectedDots, targetCount - selectedDots.length);
                selectedDots.push(...additionalDots);

                // If still not enough, use grid-based fallback for remaining dots
                if (selectedDots.length < targetCount) {
                    console.log(`Still need ${targetCount - selectedDots.length} more dots, using grid fallback`);
                    const remainingNeeded = targetCount - selectedDots.length;
                    const selectedSet = new Set(selectedDots.map(dot => `${dot.x},${dot.y}`));
                    const unselectedPoints = edgePoints.filter(point =>
                        !selectedSet.has(`${point.x},${point.y}`)
                    );

                    // Sort by strength and add the strongest remaining
                    unselectedPoints.sort((a, b) => b.strength - a.strength);
                    const finalDots = unselectedPoints.slice(0, remainingNeeded);
                    selectedDots.push(...finalDots);
                }
            }

            console.log(`Selected ${selectedDots.length} dots for dot-to-dot (target: ${targetCount})`);
            return selectedDots.slice(0, targetCount); // Ensure exact count
        }

        // Select additional dots to reach target count
        function selectAdditionalDots(allEdgePoints, selectedDots, neededCount) {
            const additionalDots = [];
            const selectedSet = new Set(selectedDots.map(dot => `${dot.x},${dot.y}`));

            // Create a list of unselected points
            const unselectedPoints = allEdgePoints.filter(point =>
                !selectedSet.has(`${point.x},${point.y}`)
            );

            // Sort by strength (strongest first)
            unselectedPoints.sort((a, b) => b.strength - a.strength);

            // Calculate minimum distance between selected dots for spacing reference
            let minSpacing = Infinity;
            for (let i = 0; i < selectedDots.length; i++) {
                for (let j = i + 1; j < selectedDots.length; j++) {
                    const dist = Math.sqrt(
                        Math.pow(selectedDots[i].x - selectedDots[j].x, 2) +
                        Math.pow(selectedDots[i].y - selectedDots[j].y, 2)
                    );
                    minSpacing = Math.min(minSpacing, dist);
                }
            }

            // Use half of minimum spacing as threshold
            const spacingThreshold = minSpacing > 0 ? minSpacing * 0.5 : 10;

            for (const point of unselectedPoints) {
                if (additionalDots.length >= neededCount) break;

                // Check if this point is far enough from all selected dots
                let farEnough = true;
                const allSelected = [...selectedDots, ...additionalDots];

                for (const selected of allSelected) {
                    const distance = Math.sqrt(
                        Math.pow(point.x - selected.x, 2) +
                        Math.pow(point.y - selected.y, 2)
                    );

                    if (distance < spacingThreshold) {
                        farEnough = false;
                        break;
                    }
                }

                if (farEnough) {
                    additionalDots.push(point);
                }
            }

            // If we still need more dots and couldn't find well-spaced ones,
            // just add the strongest remaining points
            if (additionalDots.length < neededCount) {
                for (const point of unselectedPoints) {
                    if (additionalDots.length >= neededCount) break;
                    if (!selectedSet.has(`${point.x},${point.y}`) &&
                        !additionalDots.some(dot => dot.x === point.x && dot.y === point.y)) {
                        additionalDots.push(point);
                    }
                }
            }

            return additionalDots.slice(0, neededCount);
        }

        // Grid-based dot selection fallback
        function selectDotsGridBased(edgePoints, targetCount) {
            console.log(`Grid-based selection: ${targetCount} dots from ${edgePoints.length} edge points`);

            if (edgePoints.length === 0) return [];
            if (edgePoints.length <= targetCount) return edgePoints;

            // Sort by strength (strongest edges first)
            const sortedPoints = [...edgePoints].sort((a, b) => b.strength - a.strength);

            // Calculate minimum spacing based on image dimensions and target count
            const imageArea = Math.sqrt(edgePoints.length * 100); // Rough estimate
            const minSpacing = Math.max(5, imageArea / Math.sqrt(targetCount));

            const selectedDots = [];

            for (const point of sortedPoints) {
                if (selectedDots.length >= targetCount) break;

                // Check if this point is far enough from all selected points
                let farEnough = true;
                for (const selected of selectedDots) {
                    const distance = Math.sqrt(
                        Math.pow(point.x - selected.x, 2) +
                        Math.pow(point.y - selected.y, 2)
                    );

                    if (distance < minSpacing) {
                        farEnough = false;
                        break;
                    }
                }

                if (farEnough) {
                    selectedDots.push(point);
                }
            }

            // If we still don't have enough dots, reduce spacing and try again
            if (selectedDots.length < targetCount) {
                console.log(`Only found ${selectedDots.length} well-spaced dots, reducing spacing...`);
                const reducedSpacing = minSpacing * 0.5;

                for (const point of sortedPoints) {
                    if (selectedDots.length >= targetCount) break;

                    // Skip if already selected
                    if (selectedDots.some(selected => selected.x === point.x && selected.y === point.y)) {
                        continue;
                    }

                    // Check with reduced spacing
                    let farEnough = true;
                    for (const selected of selectedDots) {
                        const distance = Math.sqrt(
                            Math.pow(point.x - selected.x, 2) +
                            Math.pow(point.y - selected.y, 2)
                        );

                        if (distance < reducedSpacing) {
                            farEnough = false;
                            break;
                        }
                    }

                    if (farEnough) {
                        selectedDots.push(point);
                    }
                }
            }

            // If we still don't have enough, just add the strongest remaining points
            if (selectedDots.length < targetCount) {
                console.log(`Still only ${selectedDots.length} dots, adding strongest remaining...`);
                const selectedSet = new Set(selectedDots.map(dot => `${dot.x},${dot.y}`));

                for (const point of sortedPoints) {
                    if (selectedDots.length >= targetCount) break;

                    if (!selectedSet.has(`${point.x},${point.y}`)) {
                        selectedDots.push(point);
                    }
                }
            }

            console.log(`Grid-based selection completed: ${selectedDots.length} dots`);
            return selectedDots.slice(0, targetCount);
        }

        // Legacy dot selection (for comparison when even spacing is disabled)
        function selectOptimalDotsLegacy(edgePoints, targetCount) {
            if (edgePoints.length <= targetCount) {
                return edgePoints;
            }

            // Sort by edge strength (strongest edges first)
            edgePoints.sort((a, b) => b.strength - a.strength);

            const selectedDots = [];
            const minDistance = Math.max(10, Math.sqrt(currentImageData.width * currentImageData.height) / targetCount);

            for (const point of edgePoints) {
                let farEnough = true;

                for (const selected of selectedDots) {
                    const distance = Math.sqrt(
                        Math.pow(point.x - selected.x, 2) +
                        Math.pow(point.y - selected.y, 2)
                    );

                    if (distance < minDistance) {
                        farEnough = false;
                        break;
                    }
                }

                if (farEnough) {
                    selectedDots.push(point);
                    if (selectedDots.length >= targetCount) {
                        break;
                    }
                }
            }

            return selectedDots;
        }

        // Organize edge points into connected contours
        function organizeIntoContours(edgePoints) {
            const contours = [];
            const used = new Set();
            const connectionDistance = 15; // Maximum distance to consider points connected

            for (const startPoint of edgePoints) {
                if (used.has(`${startPoint.x},${startPoint.y}`)) continue;

                const contour = [startPoint];
                used.add(`${startPoint.x},${startPoint.y}`);

                // Build contour by finding connected points
                let currentPoint = startPoint;
                let foundConnection = true;

                while (foundConnection) {
                    foundConnection = false;
                    let nearestPoint = null;
                    let nearestDistance = Infinity;

                    for (const point of edgePoints) {
                        const key = `${point.x},${point.y}`;
                        if (used.has(key)) continue;

                        const distance = Math.sqrt(
                            Math.pow(currentPoint.x - point.x, 2) +
                            Math.pow(currentPoint.y - point.y, 2)
                        );

                        if (distance <= connectionDistance && distance < nearestDistance) {
                            nearestDistance = distance;
                            nearestPoint = point;
                        }
                    }

                    if (nearestPoint) {
                        contour.push(nearestPoint);
                        used.add(`${nearestPoint.x},${nearestPoint.y}`);
                        currentPoint = nearestPoint;
                        foundConnection = true;
                    }
                }

                // Only keep contours with reasonable length
                if (contour.length >= 3) {
                    contours.push(contour);
                }
            }

            return contours;
        }

        // Calculate the length of a contour
        function calculateContourLength(contour) {
            if (contour.length < 2) return 0;

            let length = 0;
            for (let i = 1; i < contour.length; i++) {
                const dx = contour[i].x - contour[i-1].x;
                const dy = contour[i].y - contour[i-1].y;
                length += Math.sqrt(dx * dx + dy * dy);
            }
            return length;
        }

        // Select evenly spaced points along a contour
        function selectEvenlySpacedPoints(contour, targetCount) {
            if (contour.length <= targetCount) {
                return contour;
            }

            const totalLength = calculateContourLength(contour);
            const spacing = totalLength / (targetCount - 1);
            const selectedPoints = [contour[0]]; // Always include the first point

            let currentDistance = 0;
            let nextTargetDistance = spacing;

            for (let i = 1; i < contour.length; i++) {
                const segmentLength = Math.sqrt(
                    Math.pow(contour[i].x - contour[i-1].x, 2) +
                    Math.pow(contour[i].y - contour[i-1].y, 2)
                );

                currentDistance += segmentLength;

                if (currentDistance >= nextTargetDistance && selectedPoints.length < targetCount) {
                    selectedPoints.push(contour[i]);
                    nextTargetDistance += spacing;
                }
            }

            // Always include the last point if we haven't reached target count
            if (selectedPoints.length < targetCount && contour.length > 1) {
                const lastPoint = contour[contour.length - 1];
                if (selectedPoints[selectedPoints.length - 1] !== lastPoint) {
                    selectedPoints.push(lastPoint);
                }
            }

            return selectedPoints;
        }

        // Order dots for logical drawing sequence
        function orderDotsForDrawing(dots) {
            if (dots.length === 0) return [];

            const ordered = [];
            const remaining = [...dots];

            // Start with the top-left most point
            let current = remaining.reduce((min, point) =>
                (point.x + point.y < min.x + min.y) ? point : min
            );

            ordered.push(current);
            remaining.splice(remaining.indexOf(current), 1);

            // Use nearest neighbor algorithm for ordering
            while (remaining.length > 0) {
                let nearest = remaining[0];
                let minDistance = Infinity;

                for (const point of remaining) {
                    const distance = Math.sqrt(
                        Math.pow(current.x - point.x, 2) +
                        Math.pow(current.y - point.y, 2)
                    );

                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = point;
                    }
                }

                ordered.push(nearest);
                remaining.splice(remaining.indexOf(nearest), 1);
                current = nearest;
            }

            return ordered;
        }

        // Global variables for drag and drop
        let isDragging = false;
        let dragIndex = -1;
        let dragOffset = { x: 0, y: 0 };
        let currentDots = [];
        let currentScale = 1;
        let currentOffsetX = 0;
        let currentOffsetY = 0;
        let showBackgroundImage = true;

        // Draw the dot-to-dot on canvas
        function drawDotToDot(dots, showConnections, showBackground = true) {
            // Store current dots and settings for drag functionality
            currentDots = [...dots];
            showBackgroundImage = showBackground;

            // Clear canvas
            outputCtx.clearRect(0, 0, outputCanvas.width, outputCanvas.height);

            // Set white background
            outputCtx.fillStyle = 'white';
            outputCtx.fillRect(0, 0, outputCanvas.width, outputCanvas.height);

            if (dots.length === 0) return;

            // Calculate scaling to fit canvas
            const imageWidth = currentImageData.width;
            const imageHeight = currentImageData.height;
            const canvasWidth = outputCanvas.width;
            const canvasHeight = outputCanvas.height;

            const scaleX = (canvasWidth - 40) / imageWidth;
            const scaleY = (canvasHeight - 40) / imageHeight;
            const scale = Math.min(scaleX, scaleY);

            const offsetX = (canvasWidth - imageWidth * scale) / 2;
            const offsetY = (canvasHeight - imageHeight * scale) / 2;

            // Store scaling info for drag functionality
            currentScale = scale;
            currentOffsetX = offsetX;
            currentOffsetY = offsetY;

            // Draw background image if enabled
            if (showBackground && currentImageData) {
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = currentImageData.width;
                tempCanvas.height = currentImageData.height;

                const imageData = tempCtx.createImageData(currentImageData.width, currentImageData.height);
                imageData.data.set(currentImageData.data);
                tempCtx.putImageData(imageData, 0, 0);

                // Draw scaled and positioned background image with reduced opacity
                outputCtx.globalAlpha = 0.3;
                outputCtx.drawImage(tempCanvas, offsetX, offsetY, imageWidth * scale, imageHeight * scale);
                outputCtx.globalAlpha = 1.0;
            }

            // Transform dots to canvas coordinates
            const transformedDots = dots.map(dot => ({
                x: dot.x * scale + offsetX,
                y: dot.y * scale + offsetY
            }));

            // Draw connecting lines if enabled (only when background is shown for editing)
            if (showConnections && showBackground && transformedDots.length > 1) {
                outputCtx.strokeStyle = '#cccccc';
                outputCtx.lineWidth = 1;
                outputCtx.setLineDash([5, 5]);

                outputCtx.beginPath();
                outputCtx.moveTo(transformedDots[0].x, transformedDots[0].y);

                for (let i = 1; i < transformedDots.length; i++) {
                    outputCtx.lineTo(transformedDots[i].x, transformedDots[i].y);
                }

                outputCtx.stroke();
                outputCtx.setLineDash([]);
            }

            // Draw dots and numbers
            outputCtx.font = 'bold 14px Arial';
            outputCtx.textAlign = 'center';
            outputCtx.textBaseline = 'middle';

            transformedDots.forEach((dot, index) => {
                // Draw dot
                outputCtx.beginPath();
                outputCtx.arc(dot.x, dot.y, 4, 0, Math.PI * 2);
                outputCtx.fillStyle = '#000000';
                outputCtx.fill();

                // Draw white circle around number for better visibility
                outputCtx.beginPath();
                outputCtx.arc(dot.x + 15, dot.y - 15, 12, 0, Math.PI * 2);
                outputCtx.fillStyle = 'white';
                outputCtx.fill();
                outputCtx.strokeStyle = '#000000';
                outputCtx.lineWidth = 1;
                outputCtx.stroke();

                // Draw number
                outputCtx.fillStyle = '#000000';
                outputCtx.fillText(index + 1, dot.x + 15, dot.y - 15);
            });
        }

        // Download functions
        function downloadImage(format) {
            if (!dotToDotsData && currentDots.length === 0) {
                showStatus('No dot-to-dot to download. Please generate one first.', 'error');
                return;
            }

            let canvas;
            let dataURL;
            let filename;

            switch (format) {
                case 'png':
                    // Generate clean canvas without background or dashed lines
                    canvas = generateCleanDotToDot();
                    if (!canvas) {
                        showStatus('Error generating clean image for download.', 'error');
                        return;
                    }
                    dataURL = canvas.toDataURL('image/png');
                    filename = 'dot-to-dot.png';
                    break;
                case 'jpeg':
                    // Generate clean canvas without background or dashed lines
                    canvas = generateCleanDotToDot();
                    if (!canvas) {
                        showStatus('Error generating clean image for download.', 'error');
                        return;
                    }
                    dataURL = canvas.toDataURL('image/jpeg', 0.9);
                    filename = 'dot-to-dot.jpg';
                    break;
                case 'svg':
                    dataURL = generateCleanSVG();
                    filename = 'dot-to-dot.svg';
                    break;
                default:
                    return;
            }

            // Create download link
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showStatus(`Downloaded ${filename} successfully!`, 'success');
        }

        // Generate clean SVG version (no background, no dashed lines)
        function generateCleanSVG() {
            const dots = currentDots.length > 0 ? currentDots : (dotToDotsData ? dotToDotsData.dots : []);
            if (dots.length === 0) return '';

            const imageWidth = currentImageData ? currentImageData.width : (dotToDotsData ? dotToDotsData.originalWidth : 800);
            const imageHeight = currentImageData ? currentImageData.height : (dotToDotsData ? dotToDotsData.originalHeight : 600);
            const svgWidth = 800;
            const svgHeight = 600;

            const scaleX = (svgWidth - 40) / imageWidth;
            const scaleY = (svgHeight - 40) / imageHeight;
            const scale = Math.min(scaleX, scaleY);

            const offsetX = (svgWidth - imageWidth * scale) / 2;
            const offsetY = (svgHeight - imageHeight * scale) / 2;

            let svg = `<svg width="${svgWidth}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg">`;
            svg += `<rect width="100%" height="100%" fill="white"/>`;

            // No connecting lines in clean SVG output

            // Add dots and numbers
            dots.forEach((dot, index) => {
                const x = dot.x * scale + offsetX;
                const y = dot.y * scale + offsetY;

                // Dot
                svg += `<circle cx="${x}" cy="${y}" r="4" fill="black"/>`;

                // Number background
                svg += `<circle cx="${x + 15}" cy="${y - 15}" r="12" fill="white" stroke="black" stroke-width="1"/>`;

                // Number text
                svg += `<text x="${x + 15}" y="${y - 15}" text-anchor="middle" dominant-baseline="central" font-family="Arial" font-size="14" font-weight="bold">${index + 1}</text>`;
            });

            svg += '</svg>';

            return 'data:image/svg+xml;base64,' + btoa(svg);
        }

        // Mouse event handlers for drag and drop
        function handleMouseDown(event) {
            if (currentDots.length === 0) return;

            const rect = outputCanvas.getBoundingClientRect();
            const mouseX = event.clientX - rect.left;
            const mouseY = event.clientY - rect.top;

            // Check if mouse is over any dot
            const transformedDots = currentDots.map(dot => ({
                x: dot.x * currentScale + currentOffsetX,
                y: dot.y * currentScale + currentOffsetY
            }));

            for (let i = 0; i < transformedDots.length; i++) {
                const dot = transformedDots[i];
                const distance = Math.sqrt(
                    Math.pow(mouseX - dot.x, 2) +
                    Math.pow(mouseY - dot.y, 2)
                );

                if (distance <= 20) { // 20px click radius
                    isDragging = true;
                    dragIndex = i;
                    dragOffset.x = mouseX - dot.x;
                    dragOffset.y = mouseY - dot.y;
                    outputCanvas.style.cursor = 'grabbing';
                    break;
                }
            }
        }

        function handleMouseMove(event) {
            if (!isDragging || dragIndex === -1) {
                // Check if hovering over a dot to show pointer cursor
                if (currentDots.length > 0) {
                    const rect = outputCanvas.getBoundingClientRect();
                    const mouseX = event.clientX - rect.left;
                    const mouseY = event.clientY - rect.top;

                    const transformedDots = currentDots.map(dot => ({
                        x: dot.x * currentScale + currentOffsetX,
                        y: dot.y * currentScale + currentOffsetY
                    }));

                    let overDot = false;
                    for (const dot of transformedDots) {
                        const distance = Math.sqrt(
                            Math.pow(mouseX - dot.x, 2) +
                            Math.pow(mouseY - dot.y, 2)
                        );
                        if (distance <= 20) {
                            overDot = true;
                            break;
                        }
                    }

                    outputCanvas.style.cursor = overDot ? 'grab' : 'default';
                }
                return;
            }

            const rect = outputCanvas.getBoundingClientRect();
            const mouseX = event.clientX - rect.left;
            const mouseY = event.clientY - rect.top;

            // Update dot position
            const newCanvasX = mouseX - dragOffset.x;
            const newCanvasY = mouseY - dragOffset.y;

            // Convert back to image coordinates
            const newImageX = (newCanvasX - currentOffsetX) / currentScale;
            const newImageY = (newCanvasY - currentOffsetY) / currentScale;

            // Update the dot position
            currentDots[dragIndex].x = Math.max(0, Math.min(currentImageData.width, newImageX));
            currentDots[dragIndex].y = Math.max(0, Math.min(currentImageData.height, newImageY));

            // Redraw the canvas
            const showConnections = connectToggle.classList.contains('active');
            drawDotToDot(currentDots, showConnections, showBackgroundImage);
        }

        function handleMouseUp(event) {
            if (isDragging) {
                isDragging = false;
                dragIndex = -1;
                outputCanvas.style.cursor = 'default';

                // Update the stored dots data
                if (window.currentDotToDotsData) {
                    window.currentDotToDotsData.dots = [...currentDots];
                }
            }
        }

        // Function to generate final clean image (no background, no dashed lines)
        function generateCleanDotToDot() {
            if (currentDots.length === 0) return null;

            // Create a temporary canvas for clean output
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCanvas.width = outputCanvas.width;
            tempCanvas.height = outputCanvas.height;

            // Set white background
            tempCtx.fillStyle = 'white';
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // Transform dots to canvas coordinates
            const transformedDots = currentDots.map(dot => ({
                x: dot.x * currentScale + currentOffsetX,
                y: dot.y * currentScale + currentOffsetY
            }));

            // Draw only dots and numbers (no background, no dashed lines)
            tempCtx.font = 'bold 14px Arial';
            tempCtx.textAlign = 'center';
            tempCtx.textBaseline = 'middle';

            transformedDots.forEach((dot, index) => {
                // Draw dot
                tempCtx.beginPath();
                tempCtx.arc(dot.x, dot.y, 4, 0, Math.PI * 2);
                tempCtx.fillStyle = '#000000';
                tempCtx.fill();

                // Draw white circle around number for better visibility
                tempCtx.beginPath();
                tempCtx.arc(dot.x + 15, dot.y - 15, 12, 0, Math.PI * 2);
                tempCtx.fillStyle = 'white';
                tempCtx.fill();
                tempCtx.strokeStyle = '#000000';
                tempCtx.lineWidth = 1;
                tempCtx.stroke();

                // Draw number
                tempCtx.fillStyle = '#000000';
                tempCtx.fillText((index + 1).toString(), dot.x + 15, dot.y - 15);
            });

            return tempCanvas;
        }

        // Initialize the application when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
