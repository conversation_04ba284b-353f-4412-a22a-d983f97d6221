@echo off
echo.
echo ========================================
echo   Running Document Chapter Extractor
echo ========================================
echo.

REM Set environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set MAVEN_HOME=C:\Users\<USER>\Desktop\apache-maven-3.9.11
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%

echo Java Home: %JAVA_HOME%
echo Maven Home: %MAVEN_HOME%
echo.

REM Navigate to project
cd document-chapter-extractor

REM Test Maven first
echo Testing Maven...
"%MAVEN_HOME%\bin\mvn.cmd" --version
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Maven not working
    pause
    exit /b 1
)

echo.
echo Building application...
"%MAVEN_HOME%\bin\mvn.cmd" clean compile -DskipTests

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Starting application...
echo Application will be available at: http://localhost:8080
echo Press Ctrl+C to stop
echo.

REM Start with verbose output
"%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run -Dspring-boot.run.jvmArguments="-Dserver.port=8080"
