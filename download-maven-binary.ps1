# Download Maven Binary Distribution
Write-Host "📥 Downloading Apache Maven 3.9.11 Binary..." -ForegroundColor Green
Write-Host ""

# Maven binary download URL
$mavenUrl = "https://archive.apache.org/dist/maven/maven-3/3.9.11/binaries/apache-maven-3.9.11-bin.zip"
$mavenZip = "$env:TEMP\apache-maven-3.9.11-bin.zip"
$mavenDir = "C:\Users\<USER>\maven-binary"

Write-Host "📁 Download URL: $mavenUrl" -ForegroundColor Cyan
Write-Host "📁 Saving to: $mavenZip" -ForegroundColor Cyan
Write-Host "📁 Installing to: $mavenDir" -ForegroundColor Cyan
Write-Host ""

try {
    # Download Maven binary
    Write-Host "📥 Downloading Maven binary..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $mavenUrl -OutFile $mavenZip -UseBasicParsing
    Write-Host "✅ Download completed" -ForegroundColor Green
    Write-Host ""

    # Extract Maven
    Write-Host "📦 Extracting Maven..." -ForegroundColor Yellow
    if (Test-Path $mavenDir) {
        Remove-Item $mavenDir -Recurse -Force
    }
    
    Expand-Archive -Path $mavenZip -DestinationPath $mavenDir -Force
    
    # Find the actual Maven folder (it's usually in a subfolder)
    $mavenSubDir = Get-ChildItem -Path $mavenDir -Directory | Where-Object { $_.Name -like "apache-maven*" } | Select-Object -First 1
    $actualMavenHome = $mavenSubDir.FullName
    
    Write-Host "✅ Maven extracted to: $actualMavenHome" -ForegroundColor Green
    Write-Host ""

    # Test Maven
    Write-Host "🧪 Testing Maven..." -ForegroundColor Yellow
    $env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
    $env:MAVEN_HOME = $actualMavenHome
    $env:PATH = "$env:JAVA_HOME\bin;$env:MAVEN_HOME\bin;" + $env:PATH
    
    $mavenVersion = & "$actualMavenHome\bin\mvn.cmd" --version 2>&1
    Write-Host $mavenVersion -ForegroundColor White
    Write-Host "✅ Maven is working!" -ForegroundColor Green
    Write-Host ""

    # Clean up
    Remove-Item $mavenZip -Force
    Write-Host "🗑️  Cleaned up download file" -ForegroundColor Gray
    Write-Host ""

    # Now build and run the application
    Write-Host "🚀 Building and starting Document Chapter Extractor..." -ForegroundColor Green
    Write-Host ""
    
    Set-Location "C:\Users\<USER>\Desktop\test\document-chapter-extractor"
    
    Write-Host "🔨 Building application..." -ForegroundColor Yellow
    & "$actualMavenHome\bin\mvn.cmd" clean compile -DskipTests
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🌐 Starting server at http://localhost:8080..." -ForegroundColor Green
        Write-Host "⏹️  Press Ctrl+C to stop" -ForegroundColor Yellow
        Write-Host ""
        
        # Open browser after a delay
        Start-Job -ScriptBlock {
            Start-Sleep 15
            Start-Process "http://localhost:8080"
        } | Out-Null
        
        # Start the application
        & "$actualMavenHome\bin\mvn.cmd" spring-boot:run
        
    } else {
        Write-Host "❌ Build failed" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Manual steps:" -ForegroundColor Yellow
    Write-Host "   1. Go to https://maven.apache.org/download.cgi" -ForegroundColor White
    Write-Host "   2. Download 'Binary zip archive'" -ForegroundColor White
    Write-Host "   3. Extract to Desktop" -ForegroundColor White
    Write-Host "   4. Run this script again" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
