# Document Chapter Extractor Launcher
Write-Host "🚀 Starting Document Chapter Extractor..." -ForegroundColor Green
Write-Host ""

# Set Java environment
$env:JAVA_HOME = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
$env:PATH = "$env:JAVA_HOME\bin;" + $env:PATH

# Set Maven environment  
$env:MAVEN_HOME = "C:\Users\<USER>\maven"
$env:PATH = "$env:MAVEN_HOME\mvn\bin;" + $env:PATH

Write-Host "☕ Java Home: $env:JAVA_HOME" -ForegroundColor Cyan
Write-Host "📦 Maven Home: $env:MAVEN_HOME" -ForegroundColor Cyan
Write-Host ""

# Test Java
Write-Host "🧪 Testing Java..." -ForegroundColor Yellow
try {
    $javaVersion = & "$env:JAVA_HOME\bin\java.exe" -version 2>&1
    Write-Host "✅ Java is working!" -ForegroundColor Green
    Write-Host $javaVersion -ForegroundColor Gray
} catch {
    Write-Host "❌ Java test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Test Maven
Write-Host "🧪 Testing Maven..." -ForegroundColor Yellow
try {
    $mavenVersion = & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" --version 2>&1
    Write-Host "✅ Maven is working!" -ForegroundColor Green
    Write-Host $mavenVersion -ForegroundColor Gray
} catch {
    Write-Host "❌ Maven test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Navigate to project
Set-Location "document-chapter-extractor"
Write-Host "📁 Changed to project directory: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""

# Build the application
Write-Host "🔨 Building application..." -ForegroundColor Yellow
Write-Host "   This may take a few minutes for the first build..." -ForegroundColor Gray
Write-Host ""

try {
    & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" clean install
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Build successful!" -ForegroundColor Green
        Write-Host ""
        
        # Start the application
        Write-Host "🌐 Starting server..." -ForegroundColor Green
        Write-Host "📍 Application will be available at: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "⏹️  Press Ctrl+C to stop the server" -ForegroundColor Yellow
        Write-Host ""
        
        # Open browser after a delay
        Start-Job -ScriptBlock {
            Start-Sleep 30
            Start-Process "http://localhost:8080"
        } | Out-Null
        
        & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" spring-boot:run
        
    } else {
        Write-Host ""
        Write-Host "❌ Build failed!" -ForegroundColor Red
        Write-Host "Please check the error messages above." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
