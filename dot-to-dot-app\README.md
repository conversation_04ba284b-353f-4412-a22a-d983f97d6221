# Dot-to-Dot Creator

A complete web-based application for creating interactive dot-to-dot drawings from any image.

## Features

### Frontend
- **Clean Two-Pane Layout**: Upload & controls on the left, interactive canvas on the right
- **Drag & Drop Upload**: Support for JPG, PNG, GIF, and BMP images
- **Interactive Canvas**: Draggable dots with zoom and pan functionality
- **Real-time Editing**: Move, delete, and rearrange dots with visual feedback
- **Background Overlay**: Show original image for editing reference
- **Export Options**: Clean PNG, JPEG, and SVG exports (no background)
- **Undo/Redo**: Full history management with keyboard shortcuts
- **Dark Mode**: Toggle between light and dark themes
- **Responsive Design**: Works on desktop and mobile devices

### Backend
- **Advanced Edge Detection**: OpenCV-powered Canny edge detection
- **Smart Dot Placement**: Automatic placement along detected contours
- **Configurable Processing**: Adjustable dot count (10-300) and edge sensitivity
- **Multiple Algorithms**: Fallback to grid placement when edge detection fails
- **RESTful API**: Clean JSON API for frontend integration

### Interactive Features
- **Generate Dots**: Backend processing to create initial dot placement
- **Even Spacing**: Redistribute dots evenly along current outline
- **Auto Renumber**: Recalculate numbering after manual changes
- **Clear All**: Reset functionality
- **Drag & Drop**: Move dots by clicking and dragging
- **Zoom Controls**: Zoom in/out and reset view
- **Keyboard Shortcuts**: Ctrl+Z (undo), Ctrl+Y (redo), Delete (remove dot)

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Setup

1. **Navigate to the project directory:**
   ```bash
   cd dot-to-dot-app
   ```

2. **Install Python dependencies:**
   ```bash
   cd server
   pip install -r requirements.txt
   ```

3. **Start the server:**
   ```bash
   python app.py
   ```

4. **Open your browser and go to:**
   ```
   http://localhost:5000
   ```

## Usage

### Basic Workflow

1. **Upload Image**: Drag and drop an image or click to browse
2. **Configure Settings**: 
   - Adjust number of dots (10-300)
   - Set edge sensitivity (10-100)
   - Toggle connecting lines and background visibility
3. **Generate Dots**: Click "Generate Dots" to create initial placement
4. **Edit Manually**: 
   - Drag dots to reposition
   - Use "Even Spacing" to redistribute dots
   - Use "Renumber" to update sequence
5. **Export**: Choose PNG, JPEG, or SVG format for clean output

### Advanced Features

- **Zoom & Pan**: Use zoom controls or mouse wheel to navigate
- **Undo/Redo**: Use buttons or Ctrl+Z/Ctrl+Y keyboard shortcuts
- **Dark Mode**: Toggle with the moon/sun icon in the header
- **Background Toggle**: Show/hide original image while editing
- **Connection Lines**: Toggle dashed lines between dots

## API Endpoints

### POST /api/generate-dots
Generate dot-to-dot points from an image.

**Request:**
```json
{
  "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "dotCount": 50,
  "edgeSensitivity": 50
}
```

**Response:**
```json
{
  "success": true,
  "dots": [
    {"x": 100.5, "y": 200.3},
    {"x": 150.2, "y": 180.7}
  ],
  "count": 50
}
```

### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "service": "dot-to-dot-creator"
}
```

## File Structure

```
dot-to-dot-app/
├── index.html          # Main HTML structure
├── style.css           # Complete CSS styling
├── script.js           # Frontend JavaScript application
├── server/
│   ├── app.py          # Flask backend server
│   ├── requirements.txt # Python dependencies
│   ├── uploads/        # Temporary upload storage
│   └── outputs/        # Generated output files
└── README.md           # This file
```

## Technical Details

### Frontend Architecture
- **Vanilla JavaScript**: No external frameworks, pure ES6+ code
- **Canvas API**: HTML5 Canvas for interactive dot manipulation
- **CSS Grid**: Modern responsive layout
- **Local Storage**: Settings persistence (planned)

### Backend Architecture
- **Flask**: Lightweight Python web framework
- **OpenCV**: Advanced computer vision for edge detection
- **NumPy**: Numerical computing for image processing
- **Pillow**: Python Imaging Library for format support

### Image Processing Pipeline
1. **Preprocessing**: Convert to grayscale, apply Gaussian blur
2. **Edge Detection**: Multi-threshold Canny edge detection
3. **Contour Extraction**: Find and filter significant contours
4. **Dot Generation**: Distribute dots evenly along contour perimeters
5. **Fallback**: Grid-based placement when edge detection fails

## Browser Compatibility

- **Chrome**: 80+ (recommended)
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

## Contributing

This is a complete, production-ready application. For modifications:

1. Frontend changes: Edit `index.html`, `style.css`, or `script.js`
2. Backend changes: Modify `server/app.py`
3. Styling: Update CSS custom properties in `style.css`
4. API: Extend endpoints in `app.py`

## License

This project is provided as-is for educational and personal use.
