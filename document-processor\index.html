<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Processor - Chapter & Page Grouping Tool</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📄</text></svg>">
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1><i class="fas fa-file-text"></i> Document Processor</h1>
                <p>Upload documents, detect chapters, and group content into pages</p>
            </div>
            <div class="header-controls">
                <button id="darkModeToggle" class="btn btn-icon" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: File Upload -->
            <section class="step-section" id="uploadSection">
                <div class="step-header">
                    <h2><span class="step-number">1</span> Upload Document</h2>
                    <p>Support for PDF, Word (.docx), plain text, and EPUB files</p>
                </div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>Drag & drop your document here</h3>
                        <p>or click to browse</p>
                        <p class="file-types">Supports: PDF, DOCX, TXT, EPUB</p>
                        <input type="file" id="fileInput" accept=".pdf,.docx,.txt,.epub" hidden>

                        <div class="upload-divider">
                            <span>OR</span>
                        </div>

                        <button class="btn btn-secondary" id="manualTextBtn">
                            <i class="fas fa-keyboard"></i> Paste Text Manually
                        </button>
                    </div>
                    <div class="upload-preview" id="uploadPreview" style="display: none;">
                        <div class="file-info">
                            <i class="fas fa-file-alt"></i>
                            <div class="file-details">
                                <h4 id="fileName">document.pdf</h4>
                                <p id="fileSize">2.5 MB</p>
                                <p id="fileType">PDF Document</p>
                            </div>
                            <button class="btn btn-danger btn-small" id="removeFile">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="processing-status" id="processingStatus">
                            <div class="spinner"></div>
                            <p>Processing document...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Manual Text Input -->
            <section class="step-section" id="manualTextSection" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">1b</span> Manual Text Input</h2>
                    <p>Paste your text content and we'll detect chapters automatically</p>
                </div>

                <div class="manual-text-container">
                    <div class="text-input-area">
                        <label for="manualTextInput">Paste your document text here:</label>
                        <textarea id="manualTextInput" placeholder="Paste your document text here. Make sure chapter headings are clearly marked (e.g., 'Chapter 1', 'Chapter 2', etc.)"></textarea>
                        <div class="text-input-actions">
                            <button class="btn btn-primary" id="processManualText">
                                <i class="fas fa-magic"></i> Detect Chapters
                            </button>
                            <button class="btn btn-secondary" id="cancelManualText">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>

                    <div class="manual-text-help">
                        <h4>Tips for best results:</h4>
                        <ul>
                            <li>Use clear chapter headings like "Chapter 1", "Chapter 2"</li>
                            <li>Or use numbered sections like "1. Introduction", "2. Methods"</li>
                            <li>Make sure each chapter starts on a new line</li>
                            <li>Include the full text content for each chapter</li>
                        </ul>

                        <div class="example-format">
                            <h5>Example format:</h5>
                            <pre>Chapter 1: Introduction
This is the content of chapter 1...

Chapter 2: Methods
This is the content of chapter 2...</pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Chapter Downloads -->
            <section class="step-section" id="chapterSection" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">2</span> Download Individual Chapters</h2>
                    <p>Each chapter is automatically separated and ready for download</p>
                </div>

                <div class="chapter-actions">
                    <button class="btn btn-success" id="downloadAllChapters">
                        <i class="fas fa-download"></i> Download All Chapters as ZIP
                    </button>
                    <div class="chapter-stats-summary">
                        <span id="totalChapters">0</span> chapters found
                    </div>
                </div>

                <div class="simple-chapter-list">
                    <div class="chapter-list" id="chapterList">
                        <!-- Chapters will be populated here -->
                    </div>
                </div>
            </section>


        </main>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-message" id="statusMessage"></div>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loadingText">Processing document...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
