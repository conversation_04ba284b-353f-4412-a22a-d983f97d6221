<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Processor - Chapter & Page Grouping Tool</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📄</text></svg>">
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1><i class="fas fa-file-text"></i> Document Processor</h1>
                <p>Upload documents, detect chapters, and group content into pages</p>
            </div>
            <div class="header-controls">
                <button id="darkModeToggle" class="btn btn-icon" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: File Upload -->
            <section class="step-section" id="uploadSection">
                <div class="step-header">
                    <h2><span class="step-number">1</span> Upload Document</h2>
                    <p>Support for PDF, Word (.docx), and plain text files</p>
                </div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>Drag & drop your document here</h3>
                        <p>or click to browse</p>
                        <p class="file-types">Supports: PDF, DOCX, TXT</p>
                        <input type="file" id="fileInput" accept=".pdf,.docx,.txt" hidden>
                    </div>
                    <div class="upload-preview" id="uploadPreview" style="display: none;">
                        <div class="file-info">
                            <i class="fas fa-file-alt"></i>
                            <div class="file-details">
                                <h4 id="fileName">document.pdf</h4>
                                <p id="fileSize">2.5 MB</p>
                                <p id="fileType">PDF Document</p>
                            </div>
                            <button class="btn btn-danger btn-small" id="removeFile">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="processing-status" id="processingStatus">
                            <div class="spinner"></div>
                            <p>Processing document...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Chapter Selection -->
            <section class="step-section" id="chapterSection" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">2</span> Select Chapter</h2>
                    <p>Choose a chapter to work with</p>
                </div>
                
                <div class="chapter-list" id="chapterList">
                    <!-- Chapters will be populated here -->
                </div>
                
                <div class="chapter-preview" id="chapterPreview" style="display: none;">
                    <h3 id="selectedChapterTitle">Chapter 1: Introduction</h3>
                    <div class="chapter-stats">
                        <span class="stat"><i class="fas fa-align-left"></i> <span id="chapterWords">1,250</span> words</span>
                        <span class="stat"><i class="fas fa-file-alt"></i> <span id="chapterPages">4</span> estimated pages</span>
                    </div>
                    <div class="chapter-text" id="chapterText">
                        <!-- Chapter content preview -->
                    </div>
                </div>
            </section>

            <!-- Step 3: Page Grouping -->
            <section class="step-section" id="groupingSection" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">3</span> Group Pages</h2>
                    <p>Specify how many pages to group together</p>
                </div>
                
                <div class="grouping-controls">
                    <div class="control-group">
                        <label for="pagesPerGroup">Pages per group:</label>
                        <input type="number" id="pagesPerGroup" min="1" max="10" value="2">
                        <span class="help-text">Each page ≈ 300 words</span>
                    </div>
                    
                    <div class="control-group">
                        <label for="wordsPerPage">Words per page:</label>
                        <input type="number" id="wordsPerPage" min="100" max="1000" value="300" step="50">
                        <span class="help-text">Adjust page size estimation</span>
                    </div>
                    
                    <button class="btn btn-primary" id="generateGroups">
                        <i class="fas fa-layer-group"></i> Generate Page Groups
                    </button>
                </div>
                
                <div class="page-groups" id="pageGroups" style="display: none;">
                    <!-- Page groups will be populated here -->
                </div>
            </section>

            <!-- Step 4: Preview & Export -->
            <section class="step-section" id="exportSection" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">4</span> Preview & Export</h2>
                    <p>Review your grouped content and download</p>
                </div>
                
                <div class="export-controls">
                    <div class="export-options">
                        <button class="btn btn-success" id="exportPDF">
                            <i class="fas fa-file-pdf"></i> Export as PDF
                        </button>
                        <button class="btn btn-success" id="exportTXT">
                            <i class="fas fa-file-alt"></i> Export as Text
                        </button>
                        <button class="btn btn-success" id="exportJSON">
                            <i class="fas fa-file-code"></i> Export as JSON
                        </button>
                    </div>
                    
                    <div class="export-settings">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeHeaders" checked>
                            <span class="checkmark"></span>
                            Include page headers
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeNumbers" checked>
                            <span class="checkmark"></span>
                            Include page numbers
                        </label>
                    </div>
                </div>
                
                <div class="preview-container" id="previewContainer">
                    <div class="preview-navigation">
                        <button class="btn btn-secondary" id="prevGroup" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <span class="group-indicator" id="groupIndicator">Group 1 of 5</span>
                        <button class="btn btn-secondary" id="nextGroup">
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="preview-content" id="previewContent">
                        <!-- Preview content will be shown here -->
                    </div>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-message" id="statusMessage"></div>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loadingText">Processing document...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
