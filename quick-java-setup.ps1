# Quick Java Setup - Portable Version
Write-Host "☕ Setting up Java 17 (Portable Version)..." -ForegroundColor Green
Write-Host ""

# Download portable Java
$javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip"
$javaZip = "$env:TEMP\java17-portable.zip"
$javaDir = "C:\Users\<USER>\java17"

Write-Host "📥 Downloading portable Java 17..." -ForegroundColor Cyan

try {
    # Download Java
    Invoke-WebRequest -Uri $javaUrl -OutFile $javaZip -UseBasicParsing
    Write-Host "✅ Downloaded Java successfully" -ForegroundColor Green
    
    # Extract Java
    Write-Host "📦 Extracting Java..." -ForegroundColor Cyan
    if (Test-Path $javaDir) {
        Remove-Item $javaDir -Recurse -Force
    }
    
    Expand-Archive -Path $javaZip -DestinationPath $javaDir -Force
    
    # Find the actual Java folder (it's usually in a subfolder)
    $javaSubDir = Get-ChildItem -Path $javaDir -Directory | Select-Object -First 1
    $actualJavaHome = $javaSubDir.FullName
    
    Write-Host "✅ Java extracted to: $actualJavaHome" -ForegroundColor Green
    
    # Set environment variables for current session
    $env:JAVA_HOME = $actualJavaHome
    $env:PATH = "$actualJavaHome\bin;" + $env:PATH
    
    # Test Java
    Write-Host "🧪 Testing Java..." -ForegroundColor Cyan
    $javaVersion = & "$actualJavaHome\bin\java.exe" -version 2>&1
    Write-Host $javaVersion -ForegroundColor White
    
    Write-Host "✅ Java is ready!" -ForegroundColor Green
    
    # Clean up
    Remove-Item $javaZip -Force
    
    # Now try to run Maven
    Write-Host ""
    Write-Host "🧪 Testing Maven with Java..." -ForegroundColor Cyan
    
    $env:MAVEN_HOME = "C:\Users\<USER>\maven"
    $env:PATH = "$env:MAVEN_HOME\mvn\bin;" + $env:PATH
    
    $mavenVersion = & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" --version 2>&1
    Write-Host $mavenVersion -ForegroundColor White
    
    Write-Host "✅ Maven is working!" -ForegroundColor Green
    
    # Now build and run the application
    Write-Host ""
    Write-Host "🚀 Building and starting the Document Chapter Extractor..." -ForegroundColor Green
    Write-Host ""
    
    Set-Location "C:\Users\<USER>\Desktop\test\document-chapter-extractor"
    
    Write-Host "🔨 Building application..." -ForegroundColor Cyan
    & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" clean install
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🌐 Starting server at http://localhost:8080..." -ForegroundColor Green
        Write-Host "⏹️  Press Ctrl+C to stop" -ForegroundColor Yellow
        Write-Host ""
        
        # Start the application
        & "$env:MAVEN_HOME\mvn\bin\mvn.cmd" spring-boot:run
    } else {
        Write-Host "❌ Build failed" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Please try manual installation:" -ForegroundColor Yellow
    Write-Host "   1. Go to https://adoptium.net/" -ForegroundColor White
    Write-Host "   2. Download Java 17" -ForegroundColor White
    Write-Host "   3. Install and restart command prompt" -ForegroundColor White
}
