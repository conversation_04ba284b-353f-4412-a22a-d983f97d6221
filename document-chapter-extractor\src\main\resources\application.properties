# Server Configuration
server.port=8080
server.servlet.context-path=/

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Logging Configuration
logging.level.com.documentprocessor=INFO
logging.level.org.apache.tika=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Thymeleaf Configuration
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Application Configuration
app.name=Document Chapter Extractor
app.version=1.0.0
