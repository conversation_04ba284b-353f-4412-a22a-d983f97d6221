@echo off
echo 🚀 Installing <PERSON><PERSON> from Desktop...
echo.

REM Set variables
set DESKTOP_PATH=%USERPROFILE%\Desktop
set MAVEN_SOURCE=%DESKTOP_PATH%\maven-mvnd-1.0.2-windows-amd64
set MAVEN_HOME=C:\Program Files\Apache\Maven
set MAVEN_BIN=%MAVEN_HOME%\bin

echo 📁 Source folder: %MAVEN_SOURCE%
echo 📁 Target folder: %MAVEN_HOME%
echo.

REM Check if source folder exists
if not exist "%MAVEN_SOURCE%" (
    echo ❌ Error: Maven folder not found at %MAVEN_SOURCE%
    echo Please make sure the maven-mvnd-1.0.2-windows-amd64 folder is on your Desktop
    pause
    exit /b 1
)

echo ✅ Found Maven folder on Desktop
echo.

REM Create Program Files directory if it doesn't exist
if not exist "C:\Program Files\Apache" (
    echo 📂 Creating Apache directory...
    mkdir "C:\Program Files\Apache"
)

REM Copy Maven to Program Files (requires admin rights)
echo 📋 Copying Maven files to Program Files...
echo This may require administrator privileges...
xcopy "%MAVEN_SOURCE%" "%MAVEN_HOME%" /E /I /Y

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Error: Failed to copy Maven files. You may need to run as Administrator.
    echo.
    echo 💡 Alternative: Try running this script as Administrator
    echo    Right-click on install-maven.bat and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Maven files copied successfully
echo.

REM Add Maven to PATH
echo 🔧 Setting up environment variables...

REM Set MAVEN_HOME
setx MAVEN_HOME "%MAVEN_HOME%" /M
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Warning: Could not set MAVEN_HOME system variable. Trying user variable...
    setx MAVEN_HOME "%MAVEN_HOME%"
)

REM Get current PATH
for /f "tokens=2*" %%A in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set SYSTEM_PATH=%%B

REM Check if Maven is already in PATH
echo %SYSTEM_PATH% | find /i "%MAVEN_BIN%" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Maven is already in system PATH
) else (
    echo 📝 Adding Maven to system PATH...
    setx PATH "%SYSTEM_PATH%;%MAVEN_BIN%" /M
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠️  Warning: Could not set system PATH. Trying user PATH...
        setx PATH "%PATH%;%MAVEN_BIN%"
    )
)

echo.
echo ✅ Maven installation completed!
echo.
echo 📋 Installation Summary:
echo    Maven Home: %MAVEN_HOME%
echo    Maven Bin:  %MAVEN_BIN%
echo.
echo 🔄 Please restart your command prompt or PowerShell to use Maven
echo.
echo 🧪 To test Maven installation, open a new command prompt and run:
echo    mvn --version
echo.

pause
