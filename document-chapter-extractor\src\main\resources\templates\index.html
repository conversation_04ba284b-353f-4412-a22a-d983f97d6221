<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Chapter Extractor</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="bg-primary text-white py-4 mb-4">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="mb-0">
                            <i class="fas fa-book-open me-2"></i>
                            Document Chapter Extractor
                        </h1>
                        <p class="mb-0 opacity-75">Extract individual chapters from your documents</p>
                    </div>
                </div>
            </div>
        </header>

        <div class="container">
            <!-- Upload Section -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4" id="uploadSection">
                        <div class="card-header bg-light">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                Upload Document
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Drag and Drop Zone -->
                            <div id="dropZone" class="drop-zone text-center p-5 border-2 border-dashed rounded">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">Drag & Drop your document here</h4>
                                <p class="text-muted mb-3">or click to browse files</p>
                                <button type="button" class="btn btn-primary" id="browseBtn">
                                    <i class="fas fa-folder-open me-2"></i>Browse Files
                                </button>
                                <input type="file" id="fileInput" class="d-none" 
                                       accept=".pdf,.epub,.doc,.docx,.txt,.odt,.rtf">
                            </div>
                            
                            <!-- File Info -->
                            <div id="fileInfo" class="mt-3 d-none">
                                <div class="alert alert-info">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-file me-2"></i>
                                            <span id="fileName"></span>
                                            <small class="text-muted ms-2">(<span id="fileSize"></span>)</small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Progress Bar -->
                            <div id="progressContainer" class="mt-3 d-none">
                                <div class="progress">
                                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted mt-1 d-block" id="progressText">Uploading...</small>
                            </div>
                            
                            <!-- Upload Button -->
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-success btn-lg" id="uploadBtn" disabled>
                                    <i class="fas fa-upload me-2"></i>Process Document
                                </button>
                            </div>
                            
                            <!-- Supported Formats -->
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <strong>Supported formats:</strong> PDF, EPUB, DOC, DOCX, TXT, ODT, RTF
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div id="resultsSection" class="d-none">
                        <!-- Document Info -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-success text-white">
                                <h3 class="card-title mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    Processing Complete
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>File:</strong> <span id="resultFileName"></span></p>
                                        <p class="mb-1"><strong>Type:</strong> <span id="resultFileType"></span></p>
                                        <p class="mb-0"><strong>Size:</strong> <span id="resultFileSize"></span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>Chapters Found:</strong> <span id="totalChapters"></span></p>
                                        <p class="mb-1"><strong>Total Words:</strong> <span id="totalWords"></span></p>
                                        <button class="btn btn-primary btn-sm" id="downloadAllBtn">
                                            <i class="fas fa-download me-1"></i>Download All as ZIP
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chapters List -->
                        <div class="card shadow-sm">
                            <div class="card-header">
                                <h3 class="card-title mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Extracted Chapters
                                </h3>
                            </div>
                            <div class="card-body p-0">
                                <div id="chaptersList" class="list-group list-group-flush">
                                    <!-- Chapters will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Section -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div id="errorSection" class="alert alert-danger d-none" role="alert">
                        <h4 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Processing Error
                        </h4>
                        <p id="errorMessage" class="mb-0"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 id="loadingText">Processing document...</h5>
                    <p class="text-muted mb-0">This may take a few moments</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/js/app.js"></script>
</body>
</html>
