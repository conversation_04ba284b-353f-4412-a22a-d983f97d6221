"""
Document Processor Backend
Flask server for document parsing and chapter detection
"""

import os
import re
import io
import json
import logging
from pathlib import Path
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Document processing libraries
import PyPDF2
import docx
import fitz  # PyMuPDF for better PDF text extraction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'txt'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class DocumentProcessor:
    """Handles document parsing and chapter detection"""
    
    def __init__(self):
        # Chapter detection patterns
        self.chapter_patterns = [
            r'^chapter\s+\d+',                    # Chapter 1, Chapter 2
            r'^chapter\s+[ivxlcdm]+',             # Chapter I, Chapter II (Roman numerals)
            r'^chapter\s+[a-z]+',                 # Chapter One, Chapter Two
            r'^\d+\.\s+',                         # 1. Introduction, 2. Methods
            r'^part\s+\d+',                       # Part 1, Part 2
            r'^section\s+\d+',                    # Section 1, Section 2
            r'^[ivxlcdm]+\.\s+',                  # I. Introduction, II. Methods
            r'^\d+\s+[A-Z][a-z]+',               # 1 Introduction, 2 Methods
        ]
        
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) 
                                for pattern in self.chapter_patterns]
    
    def process_document(self, file_path, file_type):
        """
        Process document and extract text with chapter detection
        
        Args:
            file_path: Path to the uploaded file
            file_type: MIME type of the file
            
        Returns:
            Dictionary with extracted text and detected chapters
        """
        try:
            # Extract text based on file type
            logger.info(f"Processing file type: {file_type}")

            if file_type == 'application/pdf':
                text = self.extract_pdf_text(file_path)
            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                text = self.extract_docx_text(file_path)
            elif file_type == 'text/plain':
                text = self.extract_txt_text(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")

            # Log text sample for debugging
            text_sample = text[:200] if text else "No text extracted"
            logger.info(f"Extracted text sample: {text_sample}")

            # Check if text extraction was successful
            if not text or len(text.strip()) < 10:
                logger.warning("Very little text extracted from document")
                text = "Error: Could not extract readable text from this document. The file may be:\n- A scanned image (requires OCR)\n- Password protected\n- Corrupted\n- In an unsupported format"

            # Detect chapters
            chapters = self.detect_chapters(text)

            # If no chapters found, create a single chapter
            if not chapters:
                chapters = {"Full Document": text}

            logger.info(f"Processed document: {len(text)} characters, {len(chapters)} chapters")
            
            return {
                'success': True,
                'text': text,
                'chapters': chapters,
                'word_count': len(text.split()),
                'character_count': len(text)
            }
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def extract_pdf_text(self, file_path):
        """Extract text from PDF using multiple methods for better results"""
        text = ""
        extraction_methods = []

        try:
            # Method 1: PyMuPDF with different text extraction modes
            doc = fitz.open(file_path)

            # Try multiple extraction methods
            methods_to_try = [
                ("text", lambda page: page.get_text("text")),
                ("blocks", lambda page: page.get_text("blocks")),
                ("words", lambda page: page.get_text("words")),
                ("dict", lambda page: self.extract_text_from_dict(page.get_text("dict")))
            ]

            for method_name, extract_func in methods_to_try:
                method_text = ""
                try:
                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        page_text = extract_func(page)

                        if isinstance(page_text, list):  # For blocks/words methods
                            page_text = " ".join([str(item) for item in page_text if str(item).strip()])

                        if page_text and page_text.strip():
                            method_text += str(page_text) + "\n\n"

                    # Check if this method produced good results
                    if method_text.strip() and not self.is_garbled_text(method_text):
                        logger.info(f"PDF extraction successful using method: {method_name}")
                        text = method_text
                        break
                    else:
                        extraction_methods.append(f"{method_name}: {'garbled' if method_text else 'empty'}")

                except Exception as e:
                    extraction_methods.append(f"{method_name}: error - {str(e)}")
                    continue

            doc.close()

            # If PyMuPDF methods failed, try PyPDF2
            if not text.strip() or self.is_garbled_text(text):
                logger.warning("PyMuPDF methods failed, trying PyPDF2")
                try:
                    with open(file_path, 'rb') as file:
                        pdf_reader = PyPDF2.PdfReader(file)
                        pypdf_text = ""
                        for page in pdf_reader.pages:
                            page_text = page.extract_text()
                            if page_text.strip():
                                pypdf_text += page_text + "\n\n"

                        if pypdf_text.strip() and not self.is_garbled_text(pypdf_text):
                            logger.info("PDF extraction successful using PyPDF2")
                            text = pypdf_text
                        else:
                            extraction_methods.append("PyPDF2: garbled or empty")

                except Exception as e:
                    extraction_methods.append(f"PyPDF2: error - {str(e)}")

            # If all methods failed, provide helpful error message with demo content
            if not text.strip() or self.is_garbled_text(text):
                error_msg = "Unable to extract readable text from this PDF.\n\n"
                error_msg += "Possible reasons:\n"
                error_msg += "• The PDF contains scanned images (requires OCR software)\n"
                error_msg += "• The PDF is password protected\n"
                error_msg += "• The PDF uses an unsupported text encoding\n"
                error_msg += "• The PDF file is corrupted\n\n"
                error_msg += f"Extraction methods tried: {', '.join(extraction_methods)}\n\n"
                error_msg += "Suggestions:\n"
                error_msg += "• Try converting the PDF to Word format first\n"
                error_msg += "• Use OCR software if it's a scanned document\n"
                error_msg += "• Try uploading a different file format (DOCX or TXT)\n\n"

                # Add demo content so users can still test the application
                error_msg += "=" * 50 + "\n"
                error_msg += "DEMO CONTENT (for testing the application):\n"
                error_msg += "=" * 50 + "\n\n"
                error_msg += self.get_demo_content()

                text = error_msg

        except Exception as e:
            logger.error(f"PDF extraction completely failed: {e}")
            text = f"Critical error extracting text from PDF: {str(e)}\n\nPlease try a different file or format."

        return self.clean_text(text)

    def is_garbled_text(self, text):
        """Check if text appears to be garbled or corrupted"""
        if not text or len(text.strip()) < 10:
            return True

        # Clean text for analysis
        clean_text = text.strip()

        # Check for patterns that indicate garbled text
        garbled_indicators = 0
        total_checks = 0

        # 1. Check ratio of letters to total characters
        letters = sum(1 for c in clean_text if c.isalpha())
        total_chars = len(clean_text.replace(' ', '').replace('\n', ''))
        if total_chars > 0:
            letter_ratio = letters / total_chars
            if letter_ratio < 0.3:  # Less than 30% letters
                garbled_indicators += 1
            total_checks += 1

        # 2. Check for excessive punctuation/symbols
        symbols = sum(1 for c in clean_text if c in '!@#$%^&*()[]{}|\\/<>?+=~`')
        if total_chars > 0:
            symbol_ratio = symbols / total_chars
            if symbol_ratio > 0.4:  # More than 40% symbols
                garbled_indicators += 1
            total_checks += 1

        # 3. Check for lack of common English words
        words = clean_text.lower().split()
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        if len(words) > 5:
            common_word_count = sum(1 for word in words if word in common_words)
            common_ratio = common_word_count / len(words)
            if common_ratio < 0.05:  # Less than 5% common words
                garbled_indicators += 1
            total_checks += 1

        # 4. Check for excessive single characters
        single_chars = sum(1 for word in words if len(word) == 1 and word.isalpha())
        if len(words) > 0:
            single_char_ratio = single_chars / len(words)
            if single_char_ratio > 0.3:  # More than 30% single characters
                garbled_indicators += 1
            total_checks += 1

        # 5. Check for patterns like "1 23' '/0 '-." which are clearly garbled
        garbled_patterns = [
            r"[0-9]+ [0-9]+['\"]+ ['/\\]+[0-9]+ ['\-\.]+",  # Numbers with quotes and symbols
            r"['\(\)!]{3,}",  # Multiple consecutive punctuation
            r"[0-9]{1,2}['\(\)!][0-9]{1,2}['\(\)!]",  # Number-symbol-number patterns
        ]

        for pattern in garbled_patterns:
            if re.search(pattern, clean_text):
                garbled_indicators += 2  # Weight these heavily
                total_checks += 2

        # If more than 50% of checks indicate garbled text, consider it garbled
        if total_checks > 0:
            garbled_ratio = garbled_indicators / total_checks
            return garbled_ratio > 0.5

        return False

    def extract_text_from_dict(self, page_dict):
        """Extract text from PyMuPDF dictionary format"""
        text = ""

        try:
            if 'blocks' in page_dict:
                for block in page_dict['blocks']:
                    if 'lines' in block:
                        for line in block['lines']:
                            if 'spans' in line:
                                for span in line['spans']:
                                    if 'text' in span:
                                        text += span['text'] + " "
                                text += "\n"
        except Exception as e:
            logger.warning(f"Error extracting from dict format: {e}")

        return text
    
    def extract_docx_text(self, file_path):
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return self.clean_text(text)
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            raise
    
    def extract_txt_text(self, file_path):
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            return self.clean_text(text)
            
        except UnicodeDecodeError:
            # Try different encodings
            encodings = ['latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    return self.clean_text(text)
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("Could not decode text file with any supported encoding")
    
    def clean_text(self, text):
        """Clean and normalize extracted text"""
        if not text:
            return ""

        # Handle encoding issues
        try:
            # Try to encode/decode to fix encoding issues
            if isinstance(text, bytes):
                text = text.decode('utf-8', errors='ignore')
            else:
                # Re-encode and decode to clean up any encoding issues
                text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except Exception:
            pass

        # Remove excessive whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)

        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # Fix common OCR/extraction errors
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\\n\r]', ' ', text)

        # Clean up multiple spaces again after character removal
        text = re.sub(r' +', ' ', text)
        text = re.sub(r'\n +', '\n', text)
        text = re.sub(r' +\n', '\n', text)

        return text.strip()
    
    def detect_chapters(self, text):
        """
        Detect chapters in the text using various patterns
        
        Args:
            text: Full document text
            
        Returns:
            Dictionary with chapter titles as keys and content as values
        """
        chapters = {}
        lines = text.split('\n')
        
        chapter_starts = []
        
        # Find all potential chapter starts
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            # Check against all patterns
            for pattern in self.compiled_patterns:
                if pattern.match(line_stripped):
                    chapter_starts.append((i, line_stripped))
                    break
        
        # If no chapters found, return empty dict
        if not chapter_starts:
            return {}
        
        # Extract chapter content
        for i, (start_line, title) in enumerate(chapter_starts):
            # Determine end line (start of next chapter or end of document)
            if i + 1 < len(chapter_starts):
                end_line = chapter_starts[i + 1][0]
            else:
                end_line = len(lines)
            
            # Extract chapter content
            chapter_lines = lines[start_line + 1:end_line]
            chapter_content = '\n'.join(chapter_lines).strip()
            
            # Clean up chapter title
            clean_title = self.clean_chapter_title(title)
            
            # Only add if there's substantial content
            if len(chapter_content) > 100:  # At least 100 characters
                chapters[clean_title] = chapter_content
        
        return chapters
    
    def clean_chapter_title(self, title):
        """Clean and format chapter title"""
        # Remove extra whitespace
        title = re.sub(r'\s+', ' ', title.strip())
        
        # Capitalize properly
        title = title.title()
        
        return title

# Initialize document processor
processor = DocumentProcessor()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Serve the main application"""
    return send_from_directory('..', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('..', filename)

@app.route('/api/process-document', methods=['POST'])
def process_document():
    """Process uploaded document and extract chapters"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': 'File type not supported'}), 400
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({'success': False, 'error': 'File too large (max 50MB)'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        try:
            # Process the document
            result = processor.process_document(file_path, file.content_type)
            
            return jsonify(result)
            
        finally:
            # Clean up uploaded file
            try:
                os.remove(file_path)
            except OSError:
                pass
        
    except Exception as e:
        logger.error(f"Error in process_document: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'document-processor'})

if __name__ == '__main__':
    print("Starting Document Processor Server...")
    print("Frontend available at: http://localhost:5000")
    print("API available at: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
