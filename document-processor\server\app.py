"""
Document Processor Backend
Flask server for document parsing and chapter detection
"""

import os
import re
import io
import json
import logging
from pathlib import Path
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Document processing libraries
import PyPDF2
import docx
import fitz  # PyMuPDF for better PDF text extraction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'txt'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class DocumentProcessor:
    """Handles document parsing and chapter detection"""
    
    def __init__(self):
        # Chapter detection patterns
        self.chapter_patterns = [
            r'^chapter\s+\d+',                    # Chapter 1, Chapter 2
            r'^chapter\s+[ivxlcdm]+',             # Chapter I, Chapter II (Roman numerals)
            r'^chapter\s+[a-z]+',                 # Chapter One, Chapter Two
            r'^\d+\.\s+',                         # 1. Introduction, 2. Methods
            r'^part\s+\d+',                       # Part 1, Part 2
            r'^section\s+\d+',                    # Section 1, Section 2
            r'^[ivxlcdm]+\.\s+',                  # I. Introduction, II. Methods
            r'^\d+\s+[A-Z][a-z]+',               # 1 Introduction, 2 Methods
        ]
        
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) 
                                for pattern in self.chapter_patterns]
    
    def process_document(self, file_path, file_type):
        """
        Process document and extract text with chapter detection
        
        Args:
            file_path: Path to the uploaded file
            file_type: MIME type of the file
            
        Returns:
            Dictionary with extracted text and detected chapters
        """
        try:
            # Extract text based on file type
            if file_type == 'application/pdf':
                text = self.extract_pdf_text(file_path)
            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                text = self.extract_docx_text(file_path)
            elif file_type == 'text/plain':
                text = self.extract_txt_text(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            # Detect chapters
            chapters = self.detect_chapters(text)
            
            # If no chapters found, create a single chapter
            if not chapters:
                chapters = {"Full Document": text}
            
            logger.info(f"Processed document: {len(text)} characters, {len(chapters)} chapters")
            
            return {
                'success': True,
                'text': text,
                'chapters': chapters,
                'word_count': len(text.split()),
                'character_count': len(text)
            }
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def extract_pdf_text(self, file_path):
        """Extract text from PDF using PyMuPDF for better results"""
        text = ""
        
        try:
            # Try PyMuPDF first (better text extraction)
            doc = fitz.open(file_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += page.get_text()
                text += "\n\n"  # Add page breaks
            doc.close()
            
        except Exception as e:
            logger.warning(f"PyMuPDF failed, trying PyPDF2: {e}")
            
            # Fallback to PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text += page.extract_text()
                        text += "\n\n"
            except Exception as e2:
                logger.error(f"Both PDF extraction methods failed: {e2}")
                raise
        
        return self.clean_text(text)
    
    def extract_docx_text(self, file_path):
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return self.clean_text(text)
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            raise
    
    def extract_txt_text(self, file_path):
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            return self.clean_text(text)
            
        except UnicodeDecodeError:
            # Try different encodings
            encodings = ['latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    return self.clean_text(text)
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("Could not decode text file with any supported encoding")
    
    def clean_text(self, text):
        """Clean and normalize extracted text"""
        # Remove excessive whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)
        
        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        return text.strip()
    
    def detect_chapters(self, text):
        """
        Detect chapters in the text using various patterns
        
        Args:
            text: Full document text
            
        Returns:
            Dictionary with chapter titles as keys and content as values
        """
        chapters = {}
        lines = text.split('\n')
        
        chapter_starts = []
        
        # Find all potential chapter starts
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            # Check against all patterns
            for pattern in self.compiled_patterns:
                if pattern.match(line_stripped):
                    chapter_starts.append((i, line_stripped))
                    break
        
        # If no chapters found, return empty dict
        if not chapter_starts:
            return {}
        
        # Extract chapter content
        for i, (start_line, title) in enumerate(chapter_starts):
            # Determine end line (start of next chapter or end of document)
            if i + 1 < len(chapter_starts):
                end_line = chapter_starts[i + 1][0]
            else:
                end_line = len(lines)
            
            # Extract chapter content
            chapter_lines = lines[start_line + 1:end_line]
            chapter_content = '\n'.join(chapter_lines).strip()
            
            # Clean up chapter title
            clean_title = self.clean_chapter_title(title)
            
            # Only add if there's substantial content
            if len(chapter_content) > 100:  # At least 100 characters
                chapters[clean_title] = chapter_content
        
        return chapters
    
    def clean_chapter_title(self, title):
        """Clean and format chapter title"""
        # Remove extra whitespace
        title = re.sub(r'\s+', ' ', title.strip())
        
        # Capitalize properly
        title = title.title()
        
        return title

# Initialize document processor
processor = DocumentProcessor()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Serve the main application"""
    return send_from_directory('..', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('..', filename)

@app.route('/api/process-document', methods=['POST'])
def process_document():
    """Process uploaded document and extract chapters"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': 'File type not supported'}), 400
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({'success': False, 'error': 'File too large (max 50MB)'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        try:
            # Process the document
            result = processor.process_document(file_path, file.content_type)
            
            return jsonify(result)
            
        finally:
            # Clean up uploaded file
            try:
                os.remove(file_path)
            except OSError:
                pass
        
    except Exception as e:
        logger.error(f"Error in process_document: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'document-processor'})

if __name__ == '__main__':
    print("Starting Document Processor Server...")
    print("Frontend available at: http://localhost:5000")
    print("API available at: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
