"""
Document Processor Backend
Flask server for document parsing and chapter detection
"""

import os
import re
import io
import json
import logging
from pathlib import Path
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Document processing libraries
import PyPDF2
import docx
import fitz  # PyMuPDF for better PDF text extraction
import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {'pdf', 'docx', 'txt', 'epub'}

os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class DocumentProcessor:
    """Handles document parsing and chapter detection"""
    
    def __init__(self):
        # Chapter detection patterns
        self.chapter_patterns = [
            r'^chapter\s+\d+',                    # Chapter 1, Chapter 2
            r'^chapter\s+[ivxlcdm]+',             # Chapter I, Chapter II (Roman numerals)
            r'^chapter\s+[a-z]+',                 # Chapter One, Chapter Two
            r'^\d+\.\s+',                         # 1. Introduction, 2. Methods
            r'^part\s+\d+',                       # Part 1, Part 2
            r'^section\s+\d+',                    # Section 1, Section 2
            r'^[ivxlcdm]+\.\s+',                  # I. Introduction, II. Methods
            r'^\d+\s+[A-Z][a-z]+',               # 1 Introduction, 2 Methods
        ]
        
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.MULTILINE) 
                                for pattern in self.chapter_patterns]
    
    def process_document(self, file_path, file_type):
        """
        Process document and extract text with chapter detection
        
        Args:
            file_path: Path to the uploaded file
            file_type: MIME type of the file
            
        Returns:
            Dictionary with extracted text and detected chapters
        """
        try:
            # Extract text based on file type
            logger.info(f"Processing file type: {file_type}")

            if file_type == 'application/pdf':
                text = self.extract_pdf_text(file_path)
            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                text = self.extract_docx_text(file_path)
            elif file_type == 'text/plain':
                text = self.extract_txt_text(file_path)
            elif file_type == 'application/epub+zip':
                text = self.extract_epub_text(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")

            # Log text sample for debugging
            text_sample = text[:200] if text else "No text extracted"
            logger.info(f"Extracted text sample: {text_sample}")

            # Check if text extraction was successful
            if not text or len(text.strip()) < 10:
                logger.warning("Very little text extracted from document")
                text = "Error: Could not extract readable text from this document. The file may be:\n- A scanned image (requires OCR)\n- Password protected\n- Corrupted\n- In an unsupported format"

            # Detect chapters
            chapters = self.detect_chapters(text)

            # If no chapters found, create a single chapter
            if not chapters:
                chapters = {"Full Document": text}

            logger.info(f"Processed document: {len(text)} characters, {len(chapters)} chapters")
            
            return {
                'success': True,
                'text': text,
                'chapters': chapters,
                'word_count': len(text.split()),
                'character_count': len(text)
            }
            
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def extract_pdf_text(self, file_path):
        """Extract text from PDF using multiple methods for better results"""
        text = ""
        extraction_methods = []

        try:
            # Method 1: PyMuPDF with different text extraction modes
            doc = fitz.open(file_path)

            # Try multiple extraction methods
            methods_to_try = [
                ("text", lambda page: page.get_text("text")),
                ("blocks", lambda page: page.get_text("blocks")),
                ("words", lambda page: page.get_text("words")),
                ("dict", lambda page: self.extract_text_from_dict(page.get_text("dict")))
            ]

            for method_name, extract_func in methods_to_try:
                method_text = ""
                try:
                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        page_text = extract_func(page)

                        if isinstance(page_text, list):  # For blocks/words methods
                            page_text = " ".join([str(item) for item in page_text if str(item).strip()])

                        if page_text and page_text.strip():
                            method_text += str(page_text) + "\n\n"

                    # Check if this method produced good results
                    if method_text.strip() and not self.is_garbled_text(method_text):
                        logger.info(f"PDF extraction successful using method: {method_name}")
                        text = method_text
                        break
                    else:
                        extraction_methods.append(f"{method_name}: {'garbled' if method_text else 'empty'}")

                except Exception as e:
                    extraction_methods.append(f"{method_name}: error - {str(e)}")
                    continue

            # Try one more method: extract as raw text and attempt to clean
            if not text.strip() or self.is_garbled_text(text):
                try:
                    raw_text = ""
                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        # Get raw text without any processing
                        page_text = page.get_text("text", flags=0)
                        if page_text:
                            # Try to fix common encoding issues
                            cleaned_text = self.fix_encoding_issues(page_text)
                            if cleaned_text and not self.is_garbled_text(cleaned_text):
                                raw_text += cleaned_text + "\n\n"

                    if raw_text.strip() and not self.is_garbled_text(raw_text):
                        logger.info("PDF extraction successful using raw text method")
                        text = raw_text
                    else:
                        extraction_methods.append("raw_text: garbled or empty")

                except Exception as e:
                    extraction_methods.append(f"raw_text: error - {str(e)}")

            doc.close()

            # If PyMuPDF methods failed, try PyPDF2
            if not text.strip() or self.is_garbled_text(text):
                logger.warning("PyMuPDF methods failed, trying PyPDF2")
                try:
                    with open(file_path, 'rb') as file:
                        pdf_reader = PyPDF2.PdfReader(file)
                        pypdf_text = ""
                        for page in pdf_reader.pages:
                            page_text = page.extract_text()
                            if page_text.strip():
                                pypdf_text += page_text + "\n\n"

                        if pypdf_text.strip() and not self.is_garbled_text(pypdf_text):
                            logger.info("PDF extraction successful using PyPDF2")
                            text = pypdf_text
                        else:
                            extraction_methods.append("PyPDF2: garbled or empty")

                except Exception as e:
                    extraction_methods.append(f"PyPDF2: error - {str(e)}")

            # If all methods failed, provide helpful error message with demo content
            if not text.strip() or self.is_garbled_text(text):
                logger.warning(f"PDF text extraction failed or produced garbled text. Methods tried: {extraction_methods}")

                # Create a helpful error message with demo content
                error_msg = "⚠️ PDF TEXT EXTRACTION ISSUE\n\n"
                error_msg += "This PDF contains text that cannot be properly extracted. This usually happens with:\n\n"
                error_msg += "📄 Scanned documents (images of text)\n"
                error_msg += "🔒 Password-protected or encrypted PDFs\n"
                error_msg += "🔤 PDFs with unusual text encoding\n"
                error_msg += "💾 Corrupted or damaged PDF files\n\n"

                error_msg += "SOLUTIONS:\n"
                error_msg += "• Convert PDF to Word (.docx) format using online converters\n"
                error_msg += "• Use OCR software (like Adobe Acrobat) for scanned documents\n"
                error_msg += "• Try copying text directly from PDF and saving as .txt file\n"
                error_msg += "• Use a different PDF if available\n\n"

                error_msg += "=" * 60 + "\n"
                error_msg += "DEMO CONTENT - Try the app with this sample:\n"
                error_msg += "=" * 60 + "\n\n"

                # Add realistic demo content with chapters
                demo_content = self.get_demo_document()
                error_msg += demo_content

                text = error_msg

        except Exception as e:
            logger.error(f"PDF extraction completely failed: {e}")
            text = f"Critical error extracting text from PDF: {str(e)}\n\nPlease try a different file or format."

        return self.clean_text(text)

    def is_garbled_text(self, text):
        """Check if text appears to be garbled or corrupted"""
        if not text or len(text.strip()) < 10:
            return True

        # Clean text for analysis
        clean_text = text.strip()

        # Check for patterns that indicate garbled text
        garbled_indicators = 0
        total_checks = 0

        # 1. Check ratio of letters to total characters
        letters = sum(1 for c in clean_text if c.isalpha())
        total_chars = len(clean_text.replace(' ', '').replace('\n', ''))
        if total_chars > 0:
            letter_ratio = letters / total_chars
            if letter_ratio < 0.3:  # Less than 30% letters
                garbled_indicators += 1
            total_checks += 1

        # 2. Check for excessive punctuation/symbols
        symbols = sum(1 for c in clean_text if c in '!@#$%^&*()[]{}|\\/<>?+=~`')
        if total_chars > 0:
            symbol_ratio = symbols / total_chars
            if symbol_ratio > 0.4:  # More than 40% symbols
                garbled_indicators += 1
            total_checks += 1

        # 3. Check for lack of common English words
        words = clean_text.lower().split()
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        if len(words) > 5:
            common_word_count = sum(1 for word in words if word in common_words)
            common_ratio = common_word_count / len(words)
            if common_ratio < 0.05:  # Less than 5% common words
                garbled_indicators += 1
            total_checks += 1

        # 4. Check for excessive single characters
        single_chars = sum(1 for word in words if len(word) == 1 and word.isalpha())
        if len(words) > 0:
            single_char_ratio = single_chars / len(words)
            if single_char_ratio > 0.3:  # More than 30% single characters
                garbled_indicators += 1
            total_checks += 1

        # 5. Check for patterns like "1 23' '/0 '-." which are clearly garbled
        garbled_patterns = [
            r"[0-9]+ [0-9]+['\"]+ ['/\\]+[0-9]+ ['\-\.]+",  # Numbers with quotes and symbols
            r"['\(\)!]{3,}",  # Multiple consecutive punctuation
            r"[0-9]{1,2}['\(\)!][0-9]{1,2}['\(\)!]",  # Number-symbol-number patterns
        ]

        for pattern in garbled_patterns:
            if re.search(pattern, clean_text):
                garbled_indicators += 2  # Weight these heavily
                total_checks += 2

        # If more than 50% of checks indicate garbled text, consider it garbled
        if total_checks > 0:
            garbled_ratio = garbled_indicators / total_checks
            return garbled_ratio > 0.5

        return False

    def extract_text_from_dict(self, page_dict):
        """Extract text from PyMuPDF dictionary format"""
        text = ""

        try:
            if 'blocks' in page_dict:
                for block in page_dict['blocks']:
                    if 'lines' in block:
                        for line in block['lines']:
                            if 'spans' in line:
                                for span in line['spans']:
                                    if 'text' in span:
                                        text += span['text'] + " "
                                text += "\n"
        except Exception as e:
            logger.warning(f"Error extracting from dict format: {e}")

        return text
    
    def extract_docx_text(self, file_path):
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return self.clean_text(text)
            
        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            raise
    
    def extract_txt_text(self, file_path):
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
            
            return self.clean_text(text)
            
        except UnicodeDecodeError:
            # Try different encodings
            encodings = ['latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    return self.clean_text(text)
                except UnicodeDecodeError:
                    continue
            
            raise ValueError("Could not decode text file with any supported encoding")

    def extract_epub_text(self, file_path):
        """Extract text from EPUB file"""
        try:
            book = epub.read_epub(file_path)
            text = ""

            # Get all items in the book
            items = list(book.get_items())

            # Process each item
            for item in items:
                # Only process document items (HTML/XHTML content)
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    # Get the content
                    content = item.get_content()

                    # Parse HTML content with BeautifulSoup
                    soup = BeautifulSoup(content, 'html.parser')

                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()

                    # Extract text
                    item_text = soup.get_text()

                    # Clean up whitespace
                    lines = (line.strip() for line in item_text.splitlines())
                    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                    item_text = ' '.join(chunk for chunk in chunks if chunk)

                    if item_text.strip():
                        text += item_text + "\n\n"

            if not text.strip():
                raise ValueError("No readable text found in EPUB file")

            logger.info(f"Successfully extracted text from EPUB: {len(text)} characters")
            return self.clean_text(text)

        except Exception as e:
            logger.error(f"Error extracting EPUB text: {e}")
            error_msg = f"Error extracting text from EPUB: {str(e)}\n\n"
            error_msg += "Possible reasons:\n"
            error_msg += "• The EPUB file is corrupted or password protected\n"
            error_msg += "• The EPUB contains only images without text\n"
            error_msg += "• The EPUB uses an unsupported format\n\n"
            error_msg += "Suggestions:\n"
            error_msg += "• Try opening the EPUB in an e-reader to verify it contains text\n"
            error_msg += "• Try converting to PDF or DOCX format first\n"
            error_msg += "• Ensure the file is a valid EPUB format"
            return error_msg
    
    def clean_text(self, text):
        """Clean and normalize extracted text"""
        if not text:
            return ""

        # Handle encoding issues
        try:
            # Try to encode/decode to fix encoding issues
            if isinstance(text, bytes):
                text = text.decode('utf-8', errors='ignore')
            else:
                # Re-encode and decode to clean up any encoding issues
                text = text.encode('utf-8', errors='ignore').decode('utf-8')
        except Exception:
            pass

        # Remove excessive whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)

        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # Fix common OCR/extraction errors
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\\n\r]', ' ', text)

        # Clean up multiple spaces again after character removal
        text = re.sub(r' +', ' ', text)
        text = re.sub(r'\n +', '\n', text)
        text = re.sub(r' +\n', '\n', text)

        return text.strip()
    
    def detect_chapters(self, text):
        """
        Detect chapters in the text using various patterns
        
        Args:
            text: Full document text
            
        Returns:
            Dictionary with chapter titles as keys and content as values
        """
        chapters = {}
        lines = text.split('\n')
        
        chapter_starts = []
        
        # Find all potential chapter starts
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            # Check against all patterns
            for pattern in self.compiled_patterns:
                if pattern.match(line_stripped):
                    chapter_starts.append((i, line_stripped))
                    break
        
        # If no chapters found, return empty dict
        if not chapter_starts:
            return {}
        
        # Extract chapter content
        for i, (start_line, title) in enumerate(chapter_starts):
            # Determine end line (start of next chapter or end of document)
            if i + 1 < len(chapter_starts):
                end_line = chapter_starts[i + 1][0]
            else:
                end_line = len(lines)
            
            # Extract chapter content
            chapter_lines = lines[start_line + 1:end_line]
            chapter_content = '\n'.join(chapter_lines).strip()
            
            # Clean up chapter title
            clean_title = self.clean_chapter_title(title)
            
            # Only add if there's substantial content
            if len(chapter_content) > 100:  # At least 100 characters
                chapters[clean_title] = chapter_content
        
        return chapters
    
    def clean_chapter_title(self, title):
        """Clean and format chapter title"""
        # Remove extra whitespace
        title = re.sub(r'\s+', ' ', title.strip())
        
        # Capitalize properly
        title = title.title()
        
        return title

    def get_demo_document(self):
        """Generate a demo document with multiple chapters for testing"""
        demo_content = """Chapter 1: Introduction to Document Processing

Document processing is a fundamental aspect of modern information management. In today's digital age, organizations and individuals handle vast amounts of textual information stored in various formats including PDF, Word documents, and plain text files.

The ability to efficiently extract, analyze, and manipulate text from these documents has become increasingly important for businesses, researchers, and content creators. This chapter introduces the basic concepts and challenges involved in automated document processing.

Key topics covered in this chapter include text extraction methodologies, format compatibility issues, and the importance of maintaining document structure during processing. We will explore how different file formats store textual information and the various approaches used to access this data programmatically.

Chapter 2: Text Extraction Techniques

Text extraction is the process of retrieving readable text content from formatted documents. Different document formats require different extraction approaches, each with its own advantages and limitations.

For PDF documents, text extraction can be particularly challenging due to the complex internal structure of PDF files. PDFs may contain text as actual text objects, or as images that require Optical Character Recognition (OCR) to convert back to readable text.

Word documents, stored in formats like DOCX, typically provide more straightforward text extraction since they maintain a clear separation between content and formatting. The XML-based structure of modern Word documents allows for precise extraction of textual content while preserving document hierarchy.

Plain text files offer the simplest extraction scenario, requiring only basic file reading operations. However, encoding issues can still present challenges, particularly when dealing with international character sets or legacy file formats.

Chapter 3: Chapter Detection and Document Structure

Automatic chapter detection is a sophisticated process that involves pattern recognition and natural language processing techniques. The goal is to identify logical divisions within a document that represent distinct sections or chapters.

Common patterns used for chapter detection include numbered headings (Chapter 1, Chapter 2), roman numerals (I, II, III), and descriptive titles with consistent formatting. The detection algorithm must be flexible enough to handle various formatting styles while being precise enough to avoid false positives.

Document structure analysis goes beyond simple chapter detection to understand the hierarchical organization of content. This includes identifying subsections, paragraphs, lists, and other structural elements that contribute to the document's logical flow.

The preservation of document structure during processing is crucial for maintaining readability and ensuring that the extracted content remains meaningful and useful for end users.

Chapter 4: Page Grouping and Content Organization

Page grouping is the process of dividing document content into manageable chunks based on estimated page lengths. This technique is particularly useful for creating study materials, presentations, or distributing content in digestible portions.

The standard approach involves calculating an average word count per page (typically 250-300 words) and using this metric to divide content into logical page groups. However, this method must account for variations in content density, formatting, and the presence of non-textual elements.

Advanced page grouping algorithms consider factors such as paragraph boundaries, section breaks, and natural stopping points to ensure that page divisions occur at logical locations rather than arbitrary word counts.

Content organization extends beyond simple page division to include features like table of contents generation, cross-reference management, and index creation. These features enhance the usability of processed documents and provide readers with multiple ways to navigate and access information.

Chapter 5: Export Formats and Output Generation

The final stage of document processing involves generating output in various formats to meet different user needs and use cases. Common export formats include plain text, PDF, and structured data formats like JSON.

Plain text export provides the most basic output format, suitable for simple text analysis, search indexing, or integration with other text processing tools. While plain text lacks formatting information, it offers maximum compatibility and ease of processing.

PDF generation allows for the creation of formatted documents that preserve layout, typography, and visual elements. Modern PDF generation libraries provide extensive control over document appearance, enabling the creation of professional-quality output documents.

Structured data formats like JSON are particularly valuable for programmatic access to document content. JSON export can include metadata, structural information, and content hierarchy, making it ideal for integration with web applications, databases, and content management systems.

The choice of export format depends on the intended use case, with many applications offering multiple export options to accommodate different user requirements and workflow integration needs."""

        return demo_content

# Initialize document processor
processor = DocumentProcessor()

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Serve the main application"""
    return send_from_directory('..', 'index.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('..', filename)

@app.route('/api/process-document', methods=['POST'])
def process_document():
    """Process uploaded document and extract chapters"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': 'File type not supported'}), 400
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > MAX_FILE_SIZE:
            return jsonify({'success': False, 'error': 'File too large (max 50MB)'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        try:
            # Process the document
            result = processor.process_document(file_path, file.content_type)
            
            return jsonify(result)
            
        finally:
            # Clean up uploaded file
            try:
                os.remove(file_path)
            except OSError:
                pass
        
    except Exception as e:
        logger.error(f"Error in process_document: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'document-processor'})

if __name__ == '__main__':
    print("Starting Document Processor Server...")
    print("Frontend available at: http://localhost:5000")
    print("API available at: http://localhost:5000/api")
    app.run(debug=True, host='0.0.0.0', port=5000)
