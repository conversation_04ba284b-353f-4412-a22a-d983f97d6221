# Document Chapter Extractor

A full-stack Java Spring Boot application that extracts individual chapters from documents and allows users to download them separately.

## Features

### 🌐 File Upload & Drag-and-Drop
- Modern drag-and-drop interface with HTML5 support
- Traditional file upload button as fallback
- Real-time upload progress indicator
- Frontend and backend file type validation
- Support for multiple document formats

### 📄 Document Processing
- **Supported Formats**: PDF, EPUB, DOC, DOCX, TXT, ODT, RTF
- **Text Extraction**: Uses Apache Tika for robust document parsing
- **Chapter Detection**: Advanced regex patterns to identify chapter headings
- **Content Segmentation**: Automatically splits documents into individual chapters

### 📥 Chapter Downloads
- Download individual chapters as formatted text files
- Download all chapters as a ZIP archive with table of contents
- UTF-8 encoding for international character support
- Meaningful file names based on chapter titles

### 🎨 Modern UI
- Bootstrap 5 responsive design
- Font Awesome icons
- Smooth animations and transitions
- Mobile-friendly interface
- Real-time feedback and progress indicators

## Technology Stack

### Backend
- **Java 17** with Spring Boot 3.2.0
- **Apache Tika** for document parsing
- **Apache POI** for Microsoft Office documents
- **EPUBlib** for EPUB processing
- **Apache PDFBox** for PDF handling
- **Maven** for dependency management

### Frontend
- **HTML5** with drag-and-drop API
- **Bootstrap 5** for responsive UI
- **Font Awesome** for icons
- **Vanilla JavaScript** (ES6+)
- **Thymeleaf** for server-side templating

## Quick Start

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher

### Installation & Running

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd document-chapter-extractor
   ```

2. **Build the application**
   ```bash
   mvn clean install
   ```

3. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

4. **Access the application**
   ```
   Open your browser and navigate to: http://localhost:8080
   ```

## Usage

### Step 1: Upload Document
- Drag and drop a document file onto the upload zone, or
- Click "Browse Files" to select a file
- Supported formats: PDF, EPUB, DOC, DOCX, TXT, ODT, RTF
- Maximum file size: 50MB

### Step 2: Process Document
- Click "Process Document" to start extraction
- The application will automatically detect and extract chapters
- Processing time varies based on document size and complexity

### Step 3: Download Chapters
- View the list of extracted chapters with previews
- Click "Download" next to any chapter to get it as a text file
- Click "Download All as ZIP" to get all chapters in a single archive

## API Endpoints

### Upload Document
```http
POST /api/upload
Content-Type: multipart/form-data

Parameters:
- file: MultipartFile (the document to process)

Response:
{
  "success": true,
  "sessionId": "uuid",
  "fileName": "document.pdf",
  "fileType": "application/pdf",
  "fileSize": 1024000,
  "chapters": [...],
  "totalChapters": 5,
  "totalWords": 15000
}
```

### Download Individual Chapter
```http
GET /api/download/{sessionId}/{chapterId}

Response: Text file download
```

### Download All Chapters as ZIP
```http
GET /api/download-zip/{sessionId}?ids=1,2,3

Parameters:
- ids (optional): Comma-separated chapter IDs

Response: ZIP file download
```

### Cleanup Session
```http
DELETE /api/session/{sessionId}

Response: 200 OK
```

## Chapter Detection Patterns

The application uses sophisticated regex patterns to detect chapter headings:

- `Chapter 1`, `Chapter 2`, etc.
- `Chapter I`, `Chapter II` (Roman numerals)
- `Chapter One`, `Chapter Two` (written numbers)
- `1. Introduction`, `2. Methods` (numbered sections)
- `Part 1`, `Section 1`
- `I. Overview`, `II. Analysis` (Roman numeral sections)
- Special sections: `Prologue`, `Epilogue`, `Introduction`, `Conclusion`

## Configuration

### Application Properties
```properties
# Server Configuration
server.port=8080

# File Upload Limits
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# Logging
logging.level.com.documentprocessor=INFO
```

### Customization
- Modify chapter detection patterns in `DocumentProcessingService.java`
- Adjust file size limits in `application.properties`
- Customize UI styling in `static/css/style.css`

## Project Structure

```
document-chapter-extractor/
├── src/main/java/com/documentprocessor/
│   ├── DocumentChapterExtractorApplication.java
│   ├── config/
│   │   └── WebConfig.java
│   ├── controller/
│   │   ├── DocumentController.java
│   │   └── WebController.java
│   ├── model/
│   │   ├── Chapter.java
│   │   └── DocumentProcessingResult.java
│   └── service/
│       └── DocumentProcessingService.java
├── src/main/resources/
│   ├── static/
│   │   ├── css/style.css
│   │   └── js/app.js
│   ├── templates/
│   │   └── index.html
│   └── application.properties
├── pom.xml
└── README.md
```

## Error Handling

The application includes comprehensive error handling for:
- Unsupported file formats
- File size limits
- Corrupted documents
- Network errors
- Processing failures

## Security Considerations

- File type validation on both frontend and backend
- File size limits to prevent DoS attacks
- Session-based storage with automatic cleanup
- Input sanitization for chapter titles
- CORS configuration for API endpoints

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the existing issues in the repository
2. Create a new issue with detailed description
3. Include sample files if applicable (ensure no sensitive data)

## Future Enhancements

- [ ] User authentication and session management
- [ ] Database storage for processed documents
- [ ] PDF output generation for chapters
- [ ] Batch processing for multiple files
- [ ] Advanced chapter renaming interface
- [ ] OCR support for scanned documents
- [ ] Cloud storage integration
