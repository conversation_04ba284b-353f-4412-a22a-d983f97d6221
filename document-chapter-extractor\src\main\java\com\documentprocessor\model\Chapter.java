package com.documentprocessor.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class Chapter {
    private String id;
    private String title;
    private String content;
    private int wordCount;
    private int chapterNumber;
    private String preview;
    
    public Chapter() {}
    
    public Chapter(String id, String title, String content, int chapterNumber) {
        this.id = id;
        this.title = title;
        this.content = content;
        this.chapterNumber = chapterNumber;
        this.wordCount = content.split("\\s+").length;
        this.preview = generatePreview(content);
    }
    
    private String generatePreview(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "No content available";
        }
        
        String trimmed = content.trim();
        if (trimmed.length() <= 200) {
            return trimmed;
        }
        
        // Find a good breaking point near 200 characters
        int breakPoint = 200;
        while (breakPoint < trimmed.length() && 
               trimmed.charAt(breakPoint) != ' ' && 
               trimmed.charAt(breakPoint) != '.' && 
               trimmed.charAt(breakPoint) != '\n') {
            breakPoint++;
            if (breakPoint > 250) break; // Don't go too far
        }
        
        return trimmed.substring(0, Math.min(breakPoint, trimmed.length())) + "...";
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
        this.wordCount = content != null ? content.split("\\s+").length : 0;
        this.preview = generatePreview(content);
    }
    
    public int getWordCount() {
        return wordCount;
    }
    
    public void setWordCount(int wordCount) {
        this.wordCount = wordCount;
    }
    
    public int getChapterNumber() {
        return chapterNumber;
    }
    
    public void setChapterNumber(int chapterNumber) {
        this.chapterNumber = chapterNumber;
    }
    
    public String getPreview() {
        return preview;
    }
    
    public void setPreview(String preview) {
        this.preview = preview;
    }
    
    @JsonIgnore
    public String getFileName() {
        String sanitized = title.replaceAll("[^a-zA-Z0-9\\s-_]", "")
                               .replaceAll("\\s+", "_")
                               .toLowerCase();
        return String.format("Chapter_%02d_%s.txt", chapterNumber, sanitized);
    }
}
