<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2024 Holiday Calendar</title>
    <style>
        @media print {
            body { -webkit-print-color-adjust: exact; }
        }
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        select {
            padding: 8px 16px;
            font-size: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .calendar {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        .month {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 8px;
        }
        .month-name {
            text-align: center;
            font-size: 1.2em;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            text-align: center;
        }
        .day-header {
            font-weight: bold;
            padding: 5px;
            background: #f8f9fa;
        }
        .date {
            padding: 5px;
            min-height: 40px;
            position: relative;
        }
        .holiday {
            color: red;
            font-weight: bold;
        }
        .holiday-icon {
            width: 20px;
            height: 20px;
            display: block;
            margin: 2px auto;
        }
    </style>
</head>
<body>
    <h1>2024 Holiday Calendar</h1>
    <div class="controls">
        <select id="categoryFilter" onchange="filterHolidays()">
            <option value="all">All Holidays</option>
            <option value="federal">Federal Holidays</option>
            <option value="religious">Religious Holidays</option>
            <option value="cultural">Cultural Holidays</option>
            <option value="other">Other Celebrations</option>
        </select>
    </div>
    <div class="calendar" id="calendar"></div>

    <script>
        const holidays = {
            // Federal Holidays
            '2024-01-01': { name: 'New Year\'s Day', icon: createIcon('🎉'), category: 'federal' },
            '2024-01-15': { name: 'Martin Luther King Jr. Day', icon: createIcon('✊'), category: 'federal' },
            '2024-02-19': { name: 'Presidents\' Day', icon: createIcon('🎩'), category: 'federal' },
            '2024-05-27': { name: 'Memorial Day', icon: createIcon('🎖️'), category: 'federal' },
            '2024-06-19': { name: 'Juneteenth', icon: createIcon('⭐'), category: 'federal' },
            '2024-07-04': { name: 'Independence Day', icon: createIcon('🗽'), category: 'federal' },
            '2024-09-02': { name: 'Labor Day', icon: createIcon('👷'), category: 'federal' },
            '2024-10-14': { name: 'Columbus Day', icon: createIcon('🚢'), category: 'federal' },
            '2024-11-11': { name: 'Veterans Day', icon: createIcon('🎖️'), category: 'federal' },
            '2024-11-28': { name: 'Thanksgiving', icon: createIcon('🦃'), category: 'federal' },
            '2024-12-25': { name: 'Christmas', icon: createIcon('🎄'), category: 'federal' },

            // Religious Holidays
            '2024-02-14': { name: 'Ash Wednesday', icon: createIcon('✝️'), category: 'religious' },
            '2024-03-23': { name: 'Start of Ramadan', icon: createIcon('🌙'), category: 'religious' },
            '2024-03-31': { name: 'Easter', icon: createIcon('🐰'), category: 'religious' },
            '2024-04-21': { name: 'Eid al-Fitr', icon: createIcon('🕌'), category: 'religious' },
            '2024-06-28': { name: 'Eid al-Adha', icon: createIcon('🐑'), category: 'religious' },
            '2024-09-15': { name: 'Rosh Hashanah', icon: createIcon('📯'), category: 'religious' },
            '2024-09-24': { name: 'Yom Kippur', icon: createIcon('✡️'), category: 'religious' },
            '2024-12-25': { name: 'Christmas', icon: createIcon('🎄'), category: 'religious' },

            // Cultural Holidays
            '2024-02-10': { name: 'Lunar New Year', icon: createIcon('🏮'), category: 'cultural' },
            '2024-03-17': { name: 'St. Patrick\'s Day', icon: createIcon('☘️'), category: 'cultural' },
            '2024-05-05': { name: 'Cinco de Mayo', icon: createIcon('🌮'), category: 'cultural' },
            '2024-06-19': { name: 'Juneteenth', icon: createIcon('⭐'), category: 'cultural' },
            '2024-10-31': { name: 'Halloween', icon: createIcon('🎃'), category: 'cultural' },

            // Other Celebrations
            '2024-02-14': { name: 'Valentine\'s Day', icon: createIcon('❤️'), category: 'other' },
            '2024-05-12': { name: 'Mother\'s Day', icon: createIcon('💐'), category: 'other' },
            '2024-06-16': { name: 'Father\'s Day', icon: createIcon('👔'), category: 'other' },
            '2024-12-31': { name: 'New Year\'s Eve', icon: createIcon('✨'), category: 'other' }

        };

        function createIcon(emoji) {
            return `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <text x="50" y="50" text-anchor="middle" dominant-baseline="middle" font-size="80">${emoji}</text>
            </svg>`;
        }

        function filterHolidays() {
            const category = document.getElementById('categoryFilter').value;
            createCalendar(category);
        }

        function createCalendar(category = 'all') {
            const calendarDiv = document.getElementById('calendar');
            calendarDiv.innerHTML = ''; // Clear existing calendar
            const months = [];

            for (let month = 0; month < 12; month++) {
                const monthDiv = document.createElement('div');
                monthDiv.className = 'month';

                const date = new Date(2024, month, 1);
                monthDiv.innerHTML = `
                    <div class="month-name">${date.toLocaleString('default', { month: 'long' })} 2024</div>
                    <div class="days">
                        ${createDayHeaders()}
                        ${createDays(month, category)}
                    </div>
                `;

                calendarDiv.appendChild(monthDiv);
            }
        }

        function createDayHeaders() {
            const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            return days.map(day => `<div class="day-header">${day}</div>`).join('');
        }

        function createDays(month, category) {
            const firstDay = new Date(2024, month, 1);
            const lastDay = new Date(2024, month + 1, 0);
            const startingDay = firstDay.getDay();
            const totalDays = lastDay.getDate();

            let html = '';
            
            // Add empty cells for days before the first of the month
            for (let i = 0; i < startingDay; i++) {
                html += '<div class="date"></div>';
            }

            // Add the days of the month
            for (let day = 1; day <= totalDays; day++) {
                const dateStr = `2024-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const holiday = holidays[dateStr];
                if (holiday && category !== 'all' && holiday.category !== category) {
                    html += `<div class="date">${day}</div>`;
                    continue;
                }
                const holidayClass = holiday ? 'holiday' : '';
                const holidayIcon = holiday ? `<div class="holiday-icon">${holiday.icon}</div>` : '';
                const holidayName = holiday ? `<small>${holiday.name}</small>` : '';

                html += `
                    <div class="date ${holidayClass}">
                        ${day}
                        ${holidayIcon}
                        ${holidayName}
                    </div>
                `;
            }

            return html;
        }

        createCalendar();
    </script>
</body>
</html>