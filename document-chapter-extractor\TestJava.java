public class TestJava {
    public static void main(String[] args) {
        System.out.println("Java is working!");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("Java home: " + System.getProperty("java.home"));
        
        // Test if we can create a simple web server
        try {
            System.out.println("Testing basic functionality...");
            System.out.println("All tests passed!");
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
        }
    }
}
