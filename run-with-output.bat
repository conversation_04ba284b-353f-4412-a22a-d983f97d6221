@echo off
echo Starting Document Chapter Extractor...
echo.

REM Set environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set MAVEN_HOME=C:\Users\<USER>\maven
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\mvn\bin;%PATH%

echo Java Home: %JAVA_HOME%
echo Maven Home: %MAVEN_HOME%
echo.

cd document-chapter-extractor

echo Testing Java...
java -version
echo.

echo Testing Maven...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" --version
echo.

echo Starting application with output...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" spring-boot:run -Dspring-boot.run.fork=false

pause
