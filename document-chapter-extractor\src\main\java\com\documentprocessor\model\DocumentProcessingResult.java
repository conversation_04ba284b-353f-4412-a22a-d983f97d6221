package com.documentprocessor.model;

import java.util.List;

public class DocumentProcessingResult {
    private boolean success;
    private String message;
    private String sessionId;
    private String fileName;
    private String fileType;
    private long fileSize;
    private List<Chapter> chapters;
    private int totalChapters;
    private int totalWords;
    private String error;
    
    public DocumentProcessingResult() {}
    
    public DocumentProcessingResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public static DocumentProcessingResult success(String sessionId, String fileName, String fileType, 
                                                 long fileSize, List<Chapter> chapters) {
        DocumentProcessingResult result = new DocumentProcessingResult(true, "Document processed successfully");
        result.setSessionId(sessionId);
        result.setFileName(fileName);
        result.setFileType(fileType);
        result.setFileSize(fileSize);
        result.setChapters(chapters);
        result.setTotalChapters(chapters.size());
        result.setTotalWords(chapters.stream().mapToInt(Chapter::getWordCount).sum());
        return result;
    }
    
    public static DocumentProcessingResult error(String message) {
        DocumentProcessingResult result = new DocumentProcessingResult(false, message);
        result.setError(message);
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public List<Chapter> getChapters() {
        return chapters;
    }
    
    public void setChapters(List<Chapter> chapters) {
        this.chapters = chapters;
    }
    
    public int getTotalChapters() {
        return totalChapters;
    }
    
    public void setTotalChapters(int totalChapters) {
        this.totalChapters = totalChapters;
    }
    
    public int getTotalWords() {
        return totalWords;
    }
    
    public void setTotalWords(int totalWords) {
        this.totalWords = totalWords;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
}
