<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025 Holiday Calendar</title>
    <style>
        @media print {
            @page {
                size: letter landscape;
                margin: 0.5in;
            }
            .no-print {
                display: none !important;
            }
            .calendar {
                page-break-inside: avoid;
            }
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Arial, sans-serif;
        }

        body {
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.5em;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .month {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .month-header {
            background: #2c3e50;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 1.2em;
        }

        .days-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            background: #34495e;
            color: white;
            font-weight: bold;
        }

        .days-header span {
            padding: 5px;
            text-align: center;
            font-size: 0.9em;
        }

        .days-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
        }

        .day {
            padding: 8px;
            min-height: 80px;
            border: 1px solid #eee;
            position: relative;
        }

        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .holiday {
            font-size: 0.8em;
            color: #e74c3c;
            margin-top: 2px;
        }

        .holiday-icon {
            width: 24px;
            height: 24px;
            display: inline-block;
            margin-right: 5px;
            vertical-align: middle;
        }

        .controls {
            margin-bottom: 20px;
            text-align: center;
        }

        .category-filter {
            padding: 8px 15px;
            font-size: 1em;
            border: 2px solid #2c3e50;
            border-radius: 5px;
            margin-right: 10px;
            background: white;
            cursor: pointer;
        }

        .print-btn {
            padding: 8px 20px;
            background: #2c3e50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }

        .print-btn:hover {
            background: #34495e;
        }

        .holiday-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            font-size: 0.8em;
        }

        .empty-day {
            background: #f9f9f9;
        }

        @media (max-width: 1200px) {
            .calendar-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .calendar-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2025 Holiday Calendar</h1>
        
        <div class="controls no-print">
            <select class="category-filter" id="categoryFilter">
                <option value="all">All Holidays</option>
                <option value="federal">Federal Holidays</option>
                <option value="religious">Religious Holidays</option>
                <option value="cultural">Cultural Holidays</option>
                <option value="other">Other Celebrations</option>
            </select>
            <button class="print-btn" onclick="window.print()">Download PDF (Print)</button>
        </div>

        <div id="calendar" class="calendar-grid"></div>
    </div>

    <script>
        const holidays = {
            federal: {
                '2025-01-01': { name: 'New Year\'s Day', icon: '🎉' },
                '2025-01-20': { name: 'Martin Luther King Jr. Day', icon: '✊' },
                '2025-02-17': { name: 'Presidents\' Day', icon: '🎩' },
                '2025-05-26': { name: 'Memorial Day', icon: '🎖️' },
                '2025-06-19': { name: 'Juneteenth', icon: '⭐' },
                '2025-07-04': { name: 'Independence Day', icon: '🇺🇸' },
                '2025-09-01': { name: 'Labor Day', icon: '👷' },
                '2025-10-13': { name: 'Columbus Day', icon: '🚢' },
                '2025-11-11': { name: 'Veterans Day', icon: '🎖️' },
                '2025-11-27': { name: 'Thanksgiving Day', icon: '🦃' },
                '2025-12-25': { name: 'Christmas Day', icon: '🎄' }
            },
            religious: {
                '2025-03-05': { name: 'Ash Wednesday', icon: '✝️' },
                '2025-03-01': { name: 'Ramadan Begins', icon: '🌙' },
                '2025-04-20': { name: 'Easter Sunday', icon: '🐰' },
                '2025-03-31': { name: 'Eid al-Fitr', icon: '🕌' },
                '2025-06-07': { name: 'Eid al-Adha', icon: '🐑' },
                '2025-09-23': { name: 'Rosh Hashanah', icon: '📯' },
                '2025-10-02': { name: 'Yom Kippur', icon: '✡️' },
                '2025-12-24': { name: 'Christmas Eve', icon: '🎄' },
                '2025-12-25': { name: 'Christmas Day', icon: '🎄' }
            },
            cultural: {
                '2025-01-29': { name: 'Lunar New Year', icon: '🏮' },
                '2025-03-17': { name: 'St. Patrick\'s Day', icon: '☘️' },
                '2025-05-05': { name: 'Cinco de Mayo', icon: '🌮' },
                '2025-10-31': { name: 'Halloween', icon: '🎃' }
            },
            other: {
                '2025-02-14': { name: 'Valentine\'s Day', icon: '❤️' },
                '2025-05-11': { name: 'Mother\'s Day', icon: '💐' },
                '2025-06-15': { name: 'Father\'s Day', icon: '👔' },
                '2025-12-31': { name: 'New Year\'s Eve', icon: '🎆' }
            }
        };

        function createCalendar(category = 'all') {
            const calendarDiv = document.getElementById('calendar');
            calendarDiv.innerHTML = '';

            const months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

            months.forEach((month, monthIndex) => {
                const monthDiv = document.createElement('div');
                monthDiv.className = 'month';

                // Month header
                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.textContent = month;
                monthDiv.appendChild(monthHeader);

                // Days header
                const daysHeader = document.createElement('div');
                daysHeader.className = 'days-header';
                weekdays.forEach(day => {
                    const daySpan = document.createElement('span');
                    daySpan.textContent = day;
                    daysHeader.appendChild(daySpan);
                });
                monthDiv.appendChild(daysHeader);

                // Days grid
                const daysGrid = document.createElement('div');
                daysGrid.className = 'days-grid';

                // Get first day of month and total days
                const firstDay = new Date(2025, monthIndex, 1);
                const lastDay = new Date(2025, monthIndex + 1, 0);
                const totalDays = lastDay.getDate();
                const startingDay = firstDay.getDay();

                // Empty days before start of month
                for (let i = 0; i < startingDay; i++) {
                    const emptyDay = document.createElement('div');
                    emptyDay.className = 'day empty-day';
                    daysGrid.appendChild(emptyDay);
                }

                // Days of the month
                for (let day = 1; day <= totalDays; day++) {
                    const dayDiv = document.createElement('div');
                    dayDiv.className = 'day';

                    const dayNumber = document.createElement('div');
                    dayNumber.className = 'day-number';
                    dayNumber.textContent = day;
                    dayDiv.appendChild(dayNumber);

                    // Check for holidays
                    const dateStr = `2025-${String(monthIndex + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                    let dayHolidays = [];

                    if (category === 'all') {
                        Object.values(holidays).forEach(categoryHolidays => {
                            if (categoryHolidays[dateStr]) {
                                dayHolidays.push(categoryHolidays[dateStr]);
                            }
                        });
                    } else if (holidays[category] && holidays[category][dateStr]) {
                        dayHolidays.push(holidays[category][dateStr]);
                    }

                    dayHolidays.forEach(holiday => {
                        const holidayDiv = document.createElement('div');
                        holidayDiv.className = 'holiday-item';
                        holidayDiv.innerHTML = `${holiday.icon} ${holiday.name}`;
                        dayDiv.appendChild(holidayDiv);
                    });

                    daysGrid.appendChild(dayDiv);
                }

                monthDiv.appendChild(daysGrid);
                calendarDiv.appendChild(monthDiv);
            });
        }

        // Initialize calendar
        createCalendar();

        // Add event listener for category filter
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            createCalendar(e.target.value);
        });
    </script>
</body>
</html>