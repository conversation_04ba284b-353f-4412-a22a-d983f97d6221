// Dot-to-Dot Image Editor - Main JavaScript File

class DotToDotsEditor {
    constructor() {
        this.currentImage = null;
        this.dots = [];
        this.isDragging = false;
        this.dragIndex = -1;
        this.dragOffset = { x: 0, y: 0 };
        this.zoom = 1;
        this.pan = { x: 0, y: 0 };
        this.history = [];
        this.historyIndex = -1;
        this.maxHistory = 50;
        
        // Canvas elements
        this.backgroundCanvas = null;
        this.dotsCanvas = null;
        this.backgroundCtx = null;
        this.dotsCtx = null;
        
        // Settings
        this.settings = {
            dotCount: 50,
            edgeSensitivity: 50,
            showConnections: true,
            snapToOutline: false,
            showBackground: true
        };
        
        this.init();
    }
    
    init() {
        console.log('DotToDotsEditor initializing...');
        try {
            this.setupElements();
            this.setupEventListeners();
            this.setupCanvas();
            this.showStatus('Ready to upload image', 'info');
            console.log('DotToDotsEditor initialized successfully');
        } catch (error) {
            console.error('Error initializing DotToDotsEditor:', error);
        }
    }
    
    setupElements() {
        console.log('Setting up DOM elements...');
        // Get all DOM elements
        this.elements = {
            // Upload
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            uploadPreview: document.getElementById('uploadPreview'),
            previewImage: document.getElementById('previewImage'),
            removeImage: document.getElementById('removeImage'),
            
            // Controls
            dotCount: document.getElementById('dotCount'),
            dotCountValue: document.getElementById('dotCountValue'),
            edgeSensitivity: document.getElementById('edgeSensitivity'),
            edgeSensitivityValue: document.getElementById('edgeSensitivityValue'),
            showConnections: document.getElementById('showConnections'),
            snapToOutline: document.getElementById('snapToOutline'),
            showBackground: document.getElementById('showBackground'),
            
            // Buttons
            generateBtn: document.getElementById('generateBtn'),
            evenSpaceBtn: document.getElementById('evenSpaceBtn'),
            autoRenumberBtn: document.getElementById('autoRenumberBtn'),
            clearAllBtn: document.getElementById('clearAllBtn'),
            
            // Export
            exportPNG: document.getElementById('exportPNG'),
            exportJPEG: document.getElementById('exportJPEG'),
            exportSVG: document.getElementById('exportSVG'),
            
            // Canvas controls
            zoomIn: document.getElementById('zoomIn'),
            zoomOut: document.getElementById('zoomOut'),
            resetZoom: document.getElementById('resetZoom'),
            zoomLevel: document.getElementById('zoomLevel'),
            undoBtn: document.getElementById('undoBtn'),
            redoBtn: document.getElementById('redoBtn'),
            canvasInfo: document.getElementById('canvasInfo'),
            
            // Canvas
            canvasWrapper: document.getElementById('canvasWrapper'),
            backgroundCanvas: document.getElementById('backgroundCanvas'),
            dotsCanvas: document.getElementById('dotsCanvas'),
            canvasPlaceholder: document.getElementById('canvasPlaceholder'),
            
            // Status
            statusMessage: document.getElementById('statusMessage'),
            progressBar: document.getElementById('progressBar'),
            progressFill: document.getElementById('progressFill'),
            loadingOverlay: document.getElementById('loadingOverlay')
        };

        // Validate critical elements
        const criticalElements = ['uploadArea', 'fileInput', 'backgroundCanvas', 'dotsCanvas'];
        for (const elementName of criticalElements) {
            if (!this.elements[elementName]) {
                console.error(`Critical element not found: ${elementName}`);
                throw new Error(`Missing required element: ${elementName}`);
            }
        }

        console.log('All critical DOM elements found');

        this.backgroundCanvas = this.elements.backgroundCanvas;
        this.dotsCanvas = this.elements.dotsCanvas;
        this.backgroundCtx = this.backgroundCanvas.getContext('2d');
        this.dotsCtx = this.dotsCanvas.getContext('2d');
    }
    
    setupEventListeners() {
        // Upload events
        console.log('Setting up event listeners...');
        console.log('Upload area element:', this.elements.uploadArea);
        console.log('File input element:', this.elements.fileInput);

        this.elements.uploadArea.addEventListener('click', () => {
            console.log('Upload area clicked');
            this.elements.fileInput.click();
        });
        this.elements.fileInput.addEventListener('change', (e) => {
            console.log('File input changed:', e.target.files);
            this.handleFileSelect(e);
        });
        this.elements.removeImage.addEventListener('click', () => this.removeImage());
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            console.log('Drag over');
            this.handleDragOver(e);
        });
        this.elements.uploadArea.addEventListener('dragleave', (e) => {
            console.log('Drag leave');
            this.handleDragLeave(e);
        });
        this.elements.uploadArea.addEventListener('drop', (e) => {
            console.log('Drop event:', e.dataTransfer.files);
            this.handleDrop(e);
        });
        
        // Control sliders
        this.elements.dotCount.addEventListener('input', () => this.updateSliderValues());
        this.elements.edgeSensitivity.addEventListener('input', () => this.updateSliderValues());
        
        // Checkboxes
        this.elements.showConnections.addEventListener('change', () => this.updateSettings());
        this.elements.snapToOutline.addEventListener('change', () => this.updateSettings());
        this.elements.showBackground.addEventListener('change', () => this.updateSettings());
        
        // Action buttons
        this.elements.generateBtn.addEventListener('click', () => this.generateDotToDot());
        this.elements.evenSpaceBtn.addEventListener('click', () => this.evenSpaceDots());
        this.elements.autoRenumberBtn.addEventListener('click', () => this.autoRenumber());
        this.elements.clearAllBtn.addEventListener('click', () => this.clearAll());
        
        // Export buttons
        this.elements.exportPNG.addEventListener('click', () => this.exportImage('png'));
        this.elements.exportJPEG.addEventListener('click', () => this.exportImage('jpeg'));
        this.elements.exportSVG.addEventListener('click', () => this.exportImage('svg'));
        
        // Zoom controls
        this.elements.zoomIn.addEventListener('click', () => this.zoomIn());
        this.elements.zoomOut.addEventListener('click', () => this.zoomOut());
        this.elements.resetZoom.addEventListener('click', () => this.resetZoom());
        
        // Undo/Redo
        this.elements.undoBtn.addEventListener('click', () => this.undo());
        this.elements.redoBtn.addEventListener('click', () => this.redo());
        
        // Canvas mouse events
        this.dotsCanvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.dotsCanvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.dotsCanvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.dotsCanvas.addEventListener('mouseleave', (e) => this.handleMouseUp(e));
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // Window resize
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    setupCanvas() {
        this.resizeCanvas();
    }
    
    resizeCanvas() {
        const wrapper = this.elements.canvasWrapper;
        const rect = wrapper.getBoundingClientRect();
        
        const width = rect.width;
        const height = rect.height;
        
        // Set canvas size
        this.backgroundCanvas.width = width;
        this.backgroundCanvas.height = height;
        this.dotsCanvas.width = width;
        this.dotsCanvas.height = height;
        
        // Redraw if we have content
        if (this.currentImage) {
            this.drawBackground();
            this.drawDots();
        }
    }
    
    // File handling methods
    handleFileSelect(event) {
        console.log('handleFileSelect called with:', event.target.files);
        const file = event.target.files[0];
        if (file) {
            console.log('Loading file:', file.name, file.type);
            this.loadImage(file);
        } else {
            console.log('No file selected');
        }
    }
    
    handleDragOver(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.add('dragover');
    }
    
    handleDragLeave(event) {
        event.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');
    }
    
    handleDrop(event) {
        console.log('handleDrop called');
        event.preventDefault();
        this.elements.uploadArea.classList.remove('dragover');

        const files = event.dataTransfer.files;
        console.log('Dropped files:', files);
        if (files.length > 0) {
            console.log('Loading dropped file:', files[0].name, files[0].type);
            this.loadImage(files[0]);
        } else {
            console.log('No files in drop event');
        }
    }
    
    loadImage(file) {
        console.log('loadImage called with file:', file);

        // Validate file type
        if (!file.type.startsWith('image/')) {
            console.log('Invalid file type:', file.type);
            this.showStatus('Please select a valid image file', 'error');
            return;
        }

        console.log('File type valid, reading file...');
        const reader = new FileReader();
        reader.onload = (e) => {
            console.log('FileReader loaded, creating image...');
            const img = new Image();
            img.onload = () => {
                console.log('Image loaded successfully:', img.width, 'x', img.height);
                this.currentImage = img;
                this.setupImageDisplay();
                this.showStatus('Image loaded successfully', 'success');
                this.elements.generateBtn.disabled = false;
            };
            img.onerror = (error) => {
                console.error('Image load error:', error);
                this.showStatus('Error loading image', 'error');
            };
            img.src = e.target.result;
        };
        reader.onerror = (error) => {
            console.error('FileReader error:', error);
            this.showStatus('Error reading file', 'error');
        };
        reader.readAsDataURL(file);
    }
    
    setupImageDisplay() {
        // Show preview
        this.elements.previewImage.src = this.currentImage.src;
        this.elements.uploadPreview.style.display = 'block';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'none';
        
        // Hide placeholder
        this.elements.canvasPlaceholder.style.display = 'none';
        
        // Setup canvas
        this.resizeCanvas();
        this.drawBackground();
        
        // Update info
        this.elements.canvasInfo.textContent = `${this.currentImage.width} × ${this.currentImage.height}px`;
    }
    
    removeImage() {
        this.currentImage = null;
        this.dots = [];
        this.clearHistory();
        
        // Reset UI
        this.elements.uploadPreview.style.display = 'none';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'block';
        this.elements.canvasPlaceholder.style.display = 'block';
        this.elements.fileInput.value = '';
        
        // Clear canvas
        this.backgroundCtx.clearRect(0, 0, this.backgroundCanvas.width, this.backgroundCanvas.height);
        this.dotsCtx.clearRect(0, 0, this.dotsCanvas.width, this.dotsCanvas.height);
        
        // Disable buttons
        this.elements.generateBtn.disabled = true;
        this.disableEditingButtons();
        this.disableExportButtons();
        
        // Reset zoom
        this.resetZoom();
        
        this.showStatus('Image removed', 'info');
        this.elements.canvasInfo.textContent = 'Ready to upload image';
    }
    
    // UI update methods
    updateSliderValues() {
        this.elements.dotCountValue.textContent = this.elements.dotCount.value;
        this.elements.edgeSensitivityValue.textContent = this.elements.edgeSensitivity.value;
        
        this.settings.dotCount = parseInt(this.elements.dotCount.value);
        this.settings.edgeSensitivity = parseInt(this.elements.edgeSensitivity.value);
    }
    
    updateSettings() {
        this.settings.showConnections = this.elements.showConnections.checked;
        this.settings.snapToOutline = this.elements.snapToOutline.checked;
        this.settings.showBackground = this.elements.showBackground.checked;
        
        // Redraw if we have dots
        if (this.dots.length > 0) {
            this.drawBackground();
            this.drawDots();
        }
    }
    
    showStatus(message, type = 'info') {
        const statusEl = this.elements.statusMessage;
        statusEl.textContent = message;
        statusEl.className = `status-message ${type}`;
        
        // Auto-hide after 5 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }
    }
    
    showProgress(percent) {
        this.elements.progressBar.style.display = 'block';
        this.elements.progressFill.style.width = `${percent}%`;
        
        if (percent >= 100) {
            setTimeout(() => {
                this.elements.progressBar.style.display = 'none';
            }, 1000);
        }
    }
    
    showLoading(show = true) {
        this.elements.loadingOverlay.style.display = show ? 'block' : 'none';
    }
    
    enableEditingButtons() {
        this.elements.evenSpaceBtn.disabled = false;
        this.elements.autoRenumberBtn.disabled = false;
        this.elements.clearAllBtn.disabled = false;
    }
    
    disableEditingButtons() {
        this.elements.evenSpaceBtn.disabled = true;
        this.elements.autoRenumberBtn.disabled = true;
        this.elements.clearAllBtn.disabled = true;
    }
    
    enableExportButtons() {
        this.elements.exportPNG.disabled = false;
        this.elements.exportJPEG.disabled = false;
        this.elements.exportSVG.disabled = false;
    }
    
    disableExportButtons() {
        this.elements.exportPNG.disabled = true;
        this.elements.exportJPEG.disabled = true;
        this.elements.exportSVG.disabled = true;
    }
    
    // Canvas drawing methods
    drawBackground() {
        this.backgroundCtx.clearRect(0, 0, this.backgroundCanvas.width, this.backgroundCanvas.height);

        if (!this.currentImage || !this.settings.showBackground) {
            return;
        }

        const canvas = this.backgroundCanvas;
        const img = this.currentImage;

        // Calculate scaling to fit canvas while maintaining aspect ratio
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        // Center the image
        const x = (canvas.width - scaledWidth) / 2 + this.pan.x;
        const y = (canvas.height - scaledHeight) / 2 + this.pan.y;

        // Draw with reduced opacity for background
        this.backgroundCtx.globalAlpha = 0.3;
        this.backgroundCtx.drawImage(img, x, y, scaledWidth, scaledHeight);
        this.backgroundCtx.globalAlpha = 1.0;
    }

    drawDots() {
        this.dotsCtx.clearRect(0, 0, this.dotsCanvas.width, this.dotsCanvas.height);

        if (this.dots.length === 0) {
            return;
        }

        const canvas = this.dotsCanvas;
        const ctx = this.dotsCtx;

        // Draw connecting lines first (if enabled)
        if (this.settings.showConnections && this.dots.length > 1) {
            ctx.strokeStyle = '#cbd5e0';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();

            for (let i = 0; i < this.dots.length - 1; i++) {
                const current = this.transformDotToCanvas(this.dots[i]);
                const next = this.transformDotToCanvas(this.dots[i + 1]);

                if (i === 0) {
                    ctx.moveTo(current.x, current.y);
                }
                ctx.lineTo(next.x, next.y);
            }
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // Draw dots and numbers
        this.dots.forEach((dot, index) => {
            const canvasDot = this.transformDotToCanvas(dot);

            // Draw dot circle
            ctx.fillStyle = '#4a5568';
            ctx.beginPath();
            ctx.arc(canvasDot.x, canvasDot.y, 8, 0, 2 * Math.PI);
            ctx.fill();

            // Draw white border
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw number
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText((index + 1).toString(), canvasDot.x, canvasDot.y);
        });
    }

    transformDotToCanvas(dot) {
        if (!this.currentImage) return dot;

        const canvas = this.dotsCanvas;
        const img = this.currentImage;

        // Calculate scaling and positioning
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        const offsetX = (canvas.width - scaledWidth) / 2 + this.pan.x;
        const offsetY = (canvas.height - scaledHeight) / 2 + this.pan.y;

        return {
            x: dot.x * scale + offsetX,
            y: dot.y * scale + offsetY
        };
    }

    transformCanvasToDot(canvasPoint) {
        if (!this.currentImage) return canvasPoint;

        const canvas = this.dotsCanvas;
        const img = this.currentImage;

        // Calculate scaling and positioning
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height) * this.zoom;
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;

        const offsetX = (canvas.width - scaledWidth) / 2 + this.pan.x;
        const offsetY = (canvas.height - scaledHeight) / 2 + this.pan.y;

        return {
            x: (canvasPoint.x - offsetX) / scale,
            y: (canvasPoint.y - offsetY) / scale
        };
    }

    // Mouse interaction methods
    handleMouseDown(event) {
        if (!this.currentImage || this.dots.length === 0) return;

        const rect = this.dotsCanvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // Check if clicking on a dot
        for (let i = 0; i < this.dots.length; i++) {
            const canvasDot = this.transformDotToCanvas(this.dots[i]);
            const distance = Math.sqrt(
                Math.pow(mouseX - canvasDot.x, 2) +
                Math.pow(mouseY - canvasDot.y, 2)
            );

            if (distance <= 15) { // 15px click radius
                this.isDragging = true;
                this.dragIndex = i;
                this.dragOffset = {
                    x: mouseX - canvasDot.x,
                    y: mouseY - canvasDot.y
                };
                this.dotsCanvas.style.cursor = 'grabbing';
                break;
            }
        }
    }

    handleMouseMove(event) {
        const rect = this.dotsCanvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        if (this.isDragging && this.dragIndex >= 0) {
            // Update dot position
            const newCanvasPos = {
                x: mouseX - this.dragOffset.x,
                y: mouseY - this.dragOffset.y
            };

            const newDotPos = this.transformCanvasToDot(newCanvasPos);

            // Constrain to image bounds
            if (this.currentImage) {
                newDotPos.x = Math.max(0, Math.min(this.currentImage.width, newDotPos.x));
                newDotPos.y = Math.max(0, Math.min(this.currentImage.height, newDotPos.y));
            }

            this.dots[this.dragIndex] = newDotPos;
            this.drawDots();
        } else {
            // Update cursor based on hover
            let overDot = false;
            for (let i = 0; i < this.dots.length; i++) {
                const canvasDot = this.transformDotToCanvas(this.dots[i]);
                const distance = Math.sqrt(
                    Math.pow(mouseX - canvasDot.x, 2) +
                    Math.pow(mouseY - canvasDot.y, 2)
                );

                if (distance <= 15) {
                    overDot = true;
                    break;
                }
            }

            this.dotsCanvas.style.cursor = overDot ? 'grab' : 'default';
        }
    }

    handleMouseUp(event) {
        if (this.isDragging) {
            this.saveState();
            this.isDragging = false;
            this.dragIndex = -1;
            this.dotsCanvas.style.cursor = 'default';
        }
    }

    // Keyboard shortcuts
    handleKeyDown(event) {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'z':
                    event.preventDefault();
                    if (event.shiftKey) {
                        this.redo();
                    } else {
                        this.undo();
                    }
                    break;
                case 'y':
                    event.preventDefault();
                    this.redo();
                    break;
            }
        }

        // Delete key to remove selected dot
        if (event.key === 'Delete' && this.dragIndex >= 0) {
            this.deleteDot(this.dragIndex);
        }
    }

    // Zoom and pan methods
    zoomIn() {
        this.zoom = Math.min(this.zoom * 1.2, 5);
        this.updateZoomDisplay();
        this.redraw();
    }

    zoomOut() {
        this.zoom = Math.max(this.zoom / 1.2, 0.1);
        this.updateZoomDisplay();
        this.redraw();
    }

    resetZoom() {
        this.zoom = 1;
        this.pan = { x: 0, y: 0 };
        this.updateZoomDisplay();
        this.redraw();
    }

    updateZoomDisplay() {
        this.elements.zoomLevel.textContent = `${Math.round(this.zoom * 100)}%`;
    }

    redraw() {
        this.drawBackground();
        this.drawDots();
    }

    // History management
    saveState() {
        // Remove any future history if we're not at the end
        if (this.historyIndex < this.history.length - 1) {
            this.history = this.history.slice(0, this.historyIndex + 1);
        }

        // Add new state
        this.history.push(JSON.parse(JSON.stringify(this.dots)));

        // Limit history size
        if (this.history.length > this.maxHistory) {
            this.history.shift();
        } else {
            this.historyIndex++;
        }

        this.updateHistoryButtons();
    }

    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.dots = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.drawDots();
            this.updateHistoryButtons();
        }
    }

    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.dots = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.drawDots();
            this.updateHistoryButtons();
        }
    }

    updateHistoryButtons() {
        this.elements.undoBtn.disabled = this.historyIndex <= 0;
        this.elements.redoBtn.disabled = this.historyIndex >= this.history.length - 1;
    }

    clearHistory() {
        this.history = [];
        this.historyIndex = -1;
        this.updateHistoryButtons();
    }

    // Dot manipulation methods
    deleteDot(index) {
        if (index >= 0 && index < this.dots.length) {
            this.dots.splice(index, 1);
            this.saveState();
            this.drawDots();

            if (this.dots.length === 0) {
                this.disableEditingButtons();
                this.disableExportButtons();
            }
        }
    }

    autoRenumber() {
        // Dots are already in order by their array index
        // This method could be enhanced to reorder based on optimal path
        this.saveState();
        this.showStatus('Dots renumbered', 'success');
    }

    evenSpaceDots() {
        if (this.dots.length < 2) return;

        this.saveState();

        // Calculate total path length
        let totalLength = 0;
        for (let i = 0; i < this.dots.length - 1; i++) {
            const dx = this.dots[i + 1].x - this.dots[i].x;
            const dy = this.dots[i + 1].y - this.dots[i].y;
            totalLength += Math.sqrt(dx * dx + dy * dy);
        }

        // Redistribute dots evenly along the path
        const segmentLength = totalLength / (this.dots.length - 1);
        const newDots = [this.dots[0]]; // Keep first dot

        let currentLength = 0;
        let currentSegment = 0;

        for (let i = 1; i < this.dots.length - 1; i++) {
            const targetLength = i * segmentLength;

            // Find the segment where this dot should be placed
            while (currentSegment < this.dots.length - 1) {
                const dx = this.dots[currentSegment + 1].x - this.dots[currentSegment].x;
                const dy = this.dots[currentSegment + 1].y - this.dots[currentSegment].y;
                const segLen = Math.sqrt(dx * dx + dy * dy);

                if (currentLength + segLen >= targetLength) {
                    // Interpolate position within this segment
                    const ratio = (targetLength - currentLength) / segLen;
                    const newDot = {
                        x: this.dots[currentSegment].x + dx * ratio,
                        y: this.dots[currentSegment].y + dy * ratio
                    };
                    newDots.push(newDot);
                    break;
                }

                currentLength += segLen;
                currentSegment++;
            }
        }

        newDots.push(this.dots[this.dots.length - 1]); // Keep last dot
        this.dots = newDots;
        this.drawDots();
        this.showStatus('Dots evenly spaced', 'success');
    }

    clearAll() {
        if (this.dots.length === 0) return;

        if (confirm('Are you sure you want to clear all dots?')) {
            this.dots = [];
            this.clearHistory();
            this.drawDots();
            this.disableEditingButtons();
            this.disableExportButtons();
            this.showStatus('All dots cleared', 'info');
        }
    }

    // Main generation method with backend integration
    async generateDotToDot() {
        if (!this.currentImage) {
            this.showStatus('Please upload an image first', 'error');
            return;
        }

        this.showLoading(true);
        this.elements.generateBtn.disabled = true;
        this.showProgress(10);

        try {
            // Get image as base64
            const imageBase64 = this.getImageAsBase64();
            if (!imageBase64) {
                throw new Error('Failed to process image');
            }

            this.showProgress(30);

            // Send to backend for processing
            const result = await this.sendImageToBackend(imageBase64, {
                dotCount: this.settings.dotCount,
                edgeSensitivity: this.settings.edgeSensitivity
            });

            this.showProgress(80);

            console.log('Full backend result:', JSON.stringify(result, null, 2));

            if (result.success && result.dots) {
                this.dots = result.dots;
                this.drawDots();

                this.enableEditingButtons();
                this.enableExportButtons();
                this.saveState();
                this.showStatus(`Generated ${this.dots.length} dots successfully!`, 'success');
            } else {
                console.error('Backend processing failed. Result:', result);
                throw new Error(result.error || 'Backend processing failed');
            }

            this.showProgress(100);

        } catch (error) {
            console.error('Error generating dots:', error);
            this.showStatus(`Error: ${error.message}`, 'error');

            // Fallback to placeholder dots if backend fails
            console.log('Falling back to placeholder dots...');
            await this.generatePlaceholderDots();
            this.enableEditingButtons();
            this.enableExportButtons();
            this.saveState();
            this.showStatus('Generated dots using fallback method', 'info');
        } finally {
            this.showLoading(false);
            this.elements.generateBtn.disabled = false;
        }
    }

    // Placeholder dot generation (fallback)
    async generatePlaceholderDots() {
        return new Promise((resolve) => {
            setTimeout(() => {
                this.dots = [];
                const count = this.settings.dotCount;
                const margin = 50;

                for (let i = 0; i < count; i++) {
                    this.dots.push({
                        x: margin + Math.random() * (this.currentImage.width - 2 * margin),
                        y: margin + Math.random() * (this.currentImage.height - 2 * margin)
                    });
                }

                this.drawDots();
                resolve();
            }, 500);
        });
    }

    // Export methods
    exportImage(format) {
        if (this.dots.length === 0) {
            this.showStatus('No dots to export', 'error');
            return;
        }

        if (format === 'svg') {
            this.exportSVG();
        } else {
            this.exportRaster(format);
        }
    }

    exportRaster(format) {
        // Create a clean canvas for export
        const exportCanvas = document.createElement('canvas');
        const exportCtx = exportCanvas.getContext('2d');

        // Set canvas size based on original image
        exportCanvas.width = this.currentImage.width;
        exportCanvas.height = this.currentImage.height;

        // White background
        exportCtx.fillStyle = '#ffffff';
        exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);

        // Draw dots and numbers (no background image, no connecting lines)
        this.dots.forEach((dot, index) => {
            // Draw dot circle
            exportCtx.fillStyle = '#000000';
            exportCtx.beginPath();
            exportCtx.arc(dot.x, dot.y, 8, 0, 2 * Math.PI);
            exportCtx.fill();

            // Draw white border
            exportCtx.strokeStyle = '#ffffff';
            exportCtx.lineWidth = 2;
            exportCtx.stroke();

            // Draw number
            exportCtx.fillStyle = '#ffffff';
            exportCtx.font = 'bold 12px Arial';
            exportCtx.textAlign = 'center';
            exportCtx.textBaseline = 'middle';
            exportCtx.fillText((index + 1).toString(), dot.x, dot.y);
        });

        // Download
        const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
        const dataURL = exportCanvas.toDataURL(mimeType, 0.9);
        this.downloadFile(dataURL, `dot-to-dot.${format}`);

        this.showStatus(`Exported as ${format.toUpperCase()}`, 'success');
    }

    exportSVG() {
        const width = this.currentImage.width;
        const height = this.currentImage.height;

        let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
        svg += `<rect width="100%" height="100%" fill="white"/>`;

        // Add dots and numbers
        this.dots.forEach((dot, index) => {
            // Dot circle
            svg += `<circle cx="${dot.x}" cy="${dot.y}" r="8" fill="black" stroke="white" stroke-width="2"/>`;

            // Number text
            svg += `<text x="${dot.x}" y="${dot.y}" text-anchor="middle" dominant-baseline="central" `;
            svg += `font-family="Arial" font-size="12" font-weight="bold" fill="white">${index + 1}</text>`;
        });

        svg += '</svg>';

        // Create blob and download
        const blob = new Blob([svg], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        this.downloadFile(url, 'dot-to-dot.svg');

        this.showStatus('Exported as SVG', 'success');
    }

    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up object URL if it was created
        if (url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
    }

    // Snap to outline functionality
    snapDotToOutline(dotIndex) {
        if (!this.settings.snapToOutline || !this.currentImage) return;

        // This would require edge detection data from backend
        // For now, this is a placeholder
        console.log('Snap to outline not yet implemented');
    }

    // Advanced dot manipulation
    optimizeDotOrder() {
        if (this.dots.length < 3) return;

        this.saveState();

        // Simple nearest neighbor optimization
        const optimized = [this.dots[0]];
        const remaining = [...this.dots.slice(1)];

        while (remaining.length > 0) {
            const current = optimized[optimized.length - 1];
            let nearestIndex = 0;
            let nearestDistance = Infinity;

            remaining.forEach((dot, index) => {
                const distance = Math.sqrt(
                    Math.pow(dot.x - current.x, 2) +
                    Math.pow(dot.y - current.y, 2)
                );
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestIndex = index;
                }
            });

            optimized.push(remaining[nearestIndex]);
            remaining.splice(nearestIndex, 1);
        }

        this.dots = optimized;
        this.drawDots();
        this.showStatus('Dot order optimized', 'success');
    }

    // Utility methods
    getCanvasMousePosition(event) {
        const rect = this.dotsCanvas.getBoundingClientRect();
        return {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
    }

    isPointInImage(point) {
        if (!this.currentImage) return false;

        return point.x >= 0 && point.x <= this.currentImage.width &&
               point.y >= 0 && point.y <= this.currentImage.height;
    }

    // Backend communication methods
    async sendImageToBackend(imageData, settings) {
        try {
            const payload = {
                image: imageData,
                dotCount: settings.dotCount,
                edgeSensitivity: settings.edgeSensitivity,
                evenSpacing: true
            };

            console.log('Sending to backend:', {
                dotCount: settings.dotCount,
                edgeSensitivity: settings.edgeSensitivity
            });

            const response = await fetch('http://localhost:5000/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Backend error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Backend response:', result);

            return result;
        } catch (error) {
            console.error('Backend communication error:', error);
            console.error('Error details:', error.message);
            console.error('Response status:', response?.status);
            console.error('Response statusText:', response?.statusText);
            throw error;
        }
    }

    // Convert current image to base64 for backend
    getImageAsBase64() {
        if (!this.currentImage) return null;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = this.currentImage.width;
        canvas.height = this.currentImage.height;

        ctx.drawImage(this.currentImage, 0, 0);
        return canvas.toDataURL('image/png');
    }
}

// Initialize the application
console.log('Script loaded, waiting for DOM...');

function initializeApp() {
    console.log('DOM loaded, initializing DotToDotsEditor...');
    try {
        // Wait a bit more to ensure all elements are rendered
        setTimeout(() => {
            window.dotToDotsEditor = new DotToDotsEditor();
            console.log('DotToDotsEditor created successfully');
        }, 100);
    } catch (error) {
        console.error('Failed to create DotToDotsEditor:', error);
    }
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}
