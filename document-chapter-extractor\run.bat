@echo off

REM Document Chapter Extractor - Startup Script for Windows

echo 🚀 Starting Document Chapter Extractor...
echo 📋 Building application...

REM Build the application
call mvn clean install

if %ERRORLEVEL% EQU 0 (
    echo ✅ Build successful!
    echo 🌐 Starting server...
    echo 📍 Application will be available at: http://localhost:8080
    echo ⏹️  Press Ctrl+C to stop the server
    echo.
    
    REM Run the application
    call mvn spring-boot:run
) else (
    echo ❌ Build failed! Please check the error messages above.
    pause
    exit /b 1
)
