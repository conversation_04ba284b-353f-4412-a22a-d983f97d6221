@echo off
echo.
echo ========================================
echo    Maven Installation Helper
echo ========================================
echo.

REM Check if Maven folder exists on Desktop
set MAVEN_SOURCE=%USERPROFILE%\Desktop\maven-mvnd-1.0.2-windows-amd64
if not exist "%MAVEN_SOURCE%" (
    echo ❌ Maven folder not found on Desktop
    echo Looking for: %MAVEN_SOURCE%
    pause
    exit /b 1
)

echo ✅ Found Maven folder: %MAVEN_SOURCE%
echo.

REM Create a simple Maven installation in user directory
set MAVEN_HOME=%USERPROFILE%\maven
echo 📁 Installing Maven to: %MAVEN_HOME%

REM Remove existing installation if any
if exist "%MAVEN_HOME%" (
    echo 🗑️  Removing existing Maven installation...
    rmdir /s /q "%MAVEN_HOME%"
)

REM Copy Maven files
echo 📋 Copying Maven files...
xcopy "%MAVEN_SOURCE%" "%MAVEN_HOME%" /E /I /Y /Q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to copy Maven files
    pause
    exit /b 1
)

echo ✅ Maven files copied successfully
echo.

REM Set environment variables for current session
set MAVEN_HOME=%MAVEN_HOME%
set PATH=%MAVEN_HOME%\mvn\bin;%PATH%

echo 🔧 Setting up environment variables...

REM Set user environment variables (doesn't require admin)
setx MAVEN_HOME "%MAVEN_HOME%"
setx PATH "%PATH%;%MAVEN_HOME%\mvn\bin"

echo.
echo ✅ Maven installation completed!
echo.
echo 📋 Installation Details:
echo    Maven Home: %MAVEN_HOME%
echo    Maven Bin:  %MAVEN_HOME%\mvn\bin
echo.
echo 🧪 Testing Maven installation...
echo.

REM Test Maven in current session
"%MAVEN_HOME%\mvn\bin\mvn.cmd" --version

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Maven is working correctly!
    echo.
    echo 🚀 You can now run the Document Chapter Extractor:
    echo    1. Open a new Command Prompt or PowerShell
    echo    2. Navigate to: cd "%USERPROFILE%\Desktop\test\document-chapter-extractor"
    echo    3. Run: mvn clean install
    echo    4. Run: mvn spring-boot:run
    echo    5. Open browser to: http://localhost:8080
) else (
    echo.
    echo ⚠️  Maven test failed. Please restart your command prompt and try again.
)

echo.
echo 🔄 Please open a NEW command prompt to use Maven with updated PATH
pause
