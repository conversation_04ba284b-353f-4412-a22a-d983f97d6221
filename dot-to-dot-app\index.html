<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dot-to-Dot Creator - Interactive Image Editor</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1><i class="fas fa-circle-dot"></i> Dot-to-Dot Creator</h1>
                <p>Transform any image into an interactive dot-to-dot drawing</p>
            </div>
            <div class="header-controls">
                <button id="darkModeToggle" class="btn btn-icon" title="Toggle Dark Mode">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Left Panel: Upload & Controls -->
            <aside class="left-panel">
                <div class="panel-section">
                    <h3><i class="fas fa-upload"></i> Upload Image</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drag & drop an image here</p>
                            <p class="upload-subtitle">or click to browse</p>
                            <input type="file" id="fileInput" accept="image/*" hidden>
                        </div>
                        <div class="upload-preview" id="uploadPreview" style="display: none;">
                            <img id="previewImage" alt="Preview">
                            <button class="btn btn-small btn-danger" id="removeImage">
                                <i class="fas fa-times"></i> Remove
                            </button>
                        </div>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-cog"></i> Settings</h3>
                    <div class="setting-group">
                        <label for="dotCount">Number of Dots: <span id="dotCountValue">50</span></label>
                        <input type="range" id="dotCount" min="10" max="300" value="50" class="slider">
                    </div>
                    <div class="setting-group">
                        <label for="edgeSensitivity">Edge Sensitivity: <span id="edgeSensitivityValue">50</span></label>
                        <input type="range" id="edgeSensitivity" min="10" max="100" value="50" class="slider">
                    </div>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showConnections" checked>
                            <span class="checkmark"></span>
                            Show connecting lines
                        </label>
                    </div>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showBackground" checked>
                            <span class="checkmark"></span>
                            Show background image
                        </label>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-magic"></i> Actions</h3>
                    <div class="button-grid">
                        <button class="btn btn-primary" id="generateBtn" disabled>
                            <i class="fas fa-wand-magic-sparkles"></i> Generate Dots
                        </button>
                        <button class="btn btn-secondary" id="evenSpaceBtn" disabled>
                            <i class="fas fa-arrows-alt-h"></i> Even Spacing
                        </button>
                        <button class="btn btn-secondary" id="autoRenumberBtn" disabled>
                            <i class="fas fa-sort-numeric-up"></i> Renumber
                        </button>
                        <button class="btn btn-danger" id="clearAllBtn" disabled>
                            <i class="fas fa-trash"></i> Clear All
                        </button>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-download"></i> Export</h3>
                    <div class="button-grid">
                        <button class="btn btn-success" id="exportPNG" disabled>
                            <i class="fas fa-file-image"></i> PNG
                        </button>
                        <button class="btn btn-success" id="exportJPEG" disabled>
                            <i class="fas fa-file-image"></i> JPEG
                        </button>
                        <button class="btn btn-success" id="exportSVG" disabled>
                            <i class="fas fa-file-code"></i> SVG
                        </button>
                    </div>
                </div>

                <div class="panel-section">
                    <h3><i class="fas fa-history"></i> History</h3>
                    <div class="button-grid">
                        <button class="btn btn-secondary" id="undoBtn" disabled>
                            <i class="fas fa-undo"></i> Undo
                        </button>
                        <button class="btn btn-secondary" id="redoBtn" disabled>
                            <i class="fas fa-redo"></i> Redo
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Right Panel: Interactive Canvas -->
            <section class="right-panel">
                <div class="canvas-header">
                    <div class="canvas-info">
                        <span id="canvasInfo">Ready to upload image</span>
                    </div>
                    <div class="canvas-controls">
                        <button class="btn btn-icon" id="zoomOut" title="Zoom Out">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <span id="zoomLevel">100%</span>
                        <button class="btn btn-icon" id="zoomIn" title="Zoom In">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="btn btn-icon" id="resetZoom" title="Reset Zoom">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="canvas-container" id="canvasContainer">
                    <canvas id="backgroundCanvas" class="canvas-layer"></canvas>
                    <canvas id="dotsCanvas" class="canvas-layer"></canvas>
                    <div class="canvas-placeholder" id="canvasPlaceholder">
                        <i class="fas fa-image"></i>
                        <p>Upload an image to start creating your dot-to-dot</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-message" id="statusMessage"></div>
            <div class="progress-container" id="progressContainer" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Processing image...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
