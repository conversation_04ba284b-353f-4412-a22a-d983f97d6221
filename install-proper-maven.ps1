# Install Apache Maven 3.9.11 from Desktop
Write-Host "🚀 Installing Apache Maven 3.9.11..." -ForegroundColor Green
Write-Host ""

# Get Desktop path
$DesktopPath = [Environment]::GetFolderPath("Desktop")
Write-Host "📁 Desktop path: $DesktopPath" -ForegroundColor Cyan

# Look for Maven folder
$MavenFolders = @(
    "apache-maven-3.9.11",
    "apache-maven-3.9.11-bin",
    "maven-3.9.11",
    "apache-maven*"
)

$MavenSource = $null
foreach ($folder in $MavenFolders) {
    $testPath = Join-Path $DesktopPath $folder
    Write-Host "🔍 Looking for: $testPath" -ForegroundColor Gray
    
    if (Test-Path $testPath) {
        $MavenSource = $testPath
        Write-Host "✅ Found Maven at: $MavenSource" -ForegroundColor Green
        break
    }
    
    # Try wildcard search
    $wildcardResults = Get-ChildItem -Path $DesktopPath -Directory -Name $folder -ErrorAction SilentlyContinue
    if ($wildcardResults) {
        $MavenSource = Join-Path $DesktopPath $wildcardResults[0]
        Write-Host "✅ Found Maven at: $MavenSource" -ForegroundColor Green
        break
    }
}

if (-not $MavenSource) {
    Write-Host "❌ Apache Maven folder not found on Desktop" -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 Looking for folders containing 'maven'..." -ForegroundColor Yellow
    
    try {
        $allFolders = Get-ChildItem -Path $DesktopPath -Directory | Where-Object { $_.Name -like "*maven*" }
        if ($allFolders) {
            Write-Host "Found these Maven-related folders:" -ForegroundColor Cyan
            foreach ($folder in $allFolders) {
                Write-Host "  - $($folder.Name)" -ForegroundColor White
            }
            $MavenSource = $allFolders[0].FullName
            Write-Host "✅ Using: $MavenSource" -ForegroundColor Green
        } else {
            Write-Host "No Maven folders found on Desktop" -ForegroundColor Red
            Write-Host ""
            Write-Host "📋 All folders on Desktop:" -ForegroundColor Yellow
            Get-ChildItem -Path $DesktopPath -Directory | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
            Read-Host "Press Enter to exit"
            exit 1
        }
    } catch {
        Write-Host "Error searching for Maven folders: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""

# Set installation paths
$MavenHome = "C:\Program Files\Apache\Maven"
$MavenBin = Join-Path $MavenHome "bin"

Write-Host "📁 Source: $MavenSource" -ForegroundColor Cyan
Write-Host "📁 Target: $MavenHome" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  This script needs administrator privileges" -ForegroundColor Yellow
    Write-Host "🔄 Attempting to restart as administrator..." -ForegroundColor Cyan
    
    try {
        Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
        exit
    }
    catch {
        Write-Host "❌ Failed to restart as administrator" -ForegroundColor Red
        Write-Host "💡 Please run PowerShell as Administrator manually" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✅ Running with administrator privileges" -ForegroundColor Green
Write-Host ""

try {
    # Create Apache directory if it doesn't exist
    $ApacheDir = "C:\Program Files\Apache"
    if (-not (Test-Path $ApacheDir)) {
        Write-Host "📂 Creating Apache directory..." -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $ApacheDir -Force | Out-Null
    }

    # Remove existing Maven installation if any
    if (Test-Path $MavenHome) {
        Write-Host "🗑️  Removing existing Maven installation..." -ForegroundColor Yellow
        Remove-Item $MavenHome -Recurse -Force
    }

    # Copy Maven to Program Files
    Write-Host "📋 Installing Maven to Program Files..." -ForegroundColor Cyan
    Copy-Item $MavenSource $MavenHome -Recurse -Force
    Write-Host "✅ Maven files copied successfully" -ForegroundColor Green
    Write-Host ""

    # Set environment variables
    Write-Host "🔧 Setting environment variables..." -ForegroundColor Cyan
    
    # Set JAVA_HOME
    $JavaHome = "C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $JavaHome, [EnvironmentVariableTarget]::Machine)
    Write-Host "✅ JAVA_HOME set to: $JavaHome" -ForegroundColor Green
    
    # Set MAVEN_HOME
    [Environment]::SetEnvironmentVariable("MAVEN_HOME", $MavenHome, [EnvironmentVariableTarget]::Machine)
    Write-Host "✅ MAVEN_HOME set to: $MavenHome" -ForegroundColor Green
    
    # Update PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
    
    # Remove old Maven paths
    $pathParts = $currentPath -split ";" | Where-Object { $_ -notlike "*maven*" -and $_ -ne "" }
    
    # Add new Maven path
    $newPath = ($pathParts + $MavenBin) -join ";"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
    Write-Host "✅ PATH updated with Maven bin directory" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "✅ Maven installation completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Installation Summary:" -ForegroundColor Cyan
    Write-Host "   Java Home:  $JavaHome" -ForegroundColor White
    Write-Host "   Maven Home: $MavenHome" -ForegroundColor White
    Write-Host "   Maven Bin:  $MavenBin" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 Please restart your command prompt to use the new environment variables" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 To test the installation, open a new command prompt and run:" -ForegroundColor Cyan
    Write-Host "   mvn --version" -ForegroundColor White
    Write-Host ""
    Write-Host "🚀 To run the Document Chapter Extractor:" -ForegroundColor Cyan
    Write-Host "   cd document-chapter-extractor" -ForegroundColor White
    Write-Host "   mvn clean install" -ForegroundColor White
    Write-Host "   mvn spring-boot:run" -ForegroundColor White
    Write-Host ""

} catch {
    Write-Host "❌ Error during installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Make sure you're running as Administrator" -ForegroundColor White
    Write-Host "   2. Check if antivirus is blocking the operation" -ForegroundColor White
    Write-Host "   3. Ensure the Maven folder exists on Desktop" -ForegroundColor White
}

Read-Host "Press Enter to exit"
