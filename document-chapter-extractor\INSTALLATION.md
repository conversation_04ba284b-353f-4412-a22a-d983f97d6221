# Installation Guide - Document Chapter Extractor

## Prerequisites

Before running the Document Chapter Extractor, you need to install the following software:

### 1. Java Development Kit (JDK) 17 or higher

#### Windows:
1. Download JDK 17 from [Oracle](https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html) or [OpenJDK](https://adoptium.net/)
2. Run the installer and follow the setup wizard
3. Verify installation by opening Command Prompt and running:
   ```cmd
   java -version
   javac -version
   ```

#### macOS:
1. Install using Homebrew:
   ```bash
   brew install openjdk@17
   ```
2. Or download from [Adoptium](https://adoptium.net/)

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install openjdk-17-jdk
```

### 2. Apache Maven 3.6 or higher

#### Windows:
1. Download Maven from [Apache Maven](https://maven.apache.org/download.cgi)
2. Extract to a folder (e.g., `C:\Program Files\Apache\maven`)
3. Add Maven's `bin` directory to your PATH environment variable
4. Verify installation:
   ```cmd
   mvn -version
   ```

#### macOS:
```bash
brew install maven
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install maven
```

## Installation Steps

### Step 1: Download the Project
```bash
# If using Git
git clone <repository-url>
cd document-chapter-extractor

# Or download and extract the ZIP file
```

### Step 2: Build the Application
```bash
mvn clean install
```

This command will:
- Download all required dependencies
- Compile the Java source code
- Run tests
- Package the application

### Step 3: Run the Application

#### Option A: Using Maven (Recommended for Development)
```bash
mvn spring-boot:run
```

#### Option B: Using the JAR file
```bash
# After building, find the JAR in target/ directory
java -jar target/document-chapter-extractor-1.0.0.jar
```

#### Option C: Using the provided scripts

**Windows:**
```cmd
run.bat
```

**Linux/macOS:**
```bash
chmod +x run.sh
./run.sh
```

### Step 4: Access the Application
Open your web browser and navigate to:
```
http://localhost:8080
```

## Troubleshooting

### Common Issues

#### 1. "mvn command not found"
- Ensure Maven is installed and added to your PATH
- Restart your terminal/command prompt after installation

#### 2. "JAVA_HOME not set"
- Set the JAVA_HOME environment variable to your JDK installation directory
- Example (Windows): `JAVA_HOME=C:\Program Files\Java\jdk-17`
- Example (Linux/macOS): `JAVA_HOME=/usr/lib/jvm/java-17-openjdk`

#### 3. Port 8080 already in use
- Change the port in `src/main/resources/application.properties`:
  ```properties
  server.port=8081
  ```
- Or run with a different port:
  ```bash
  mvn spring-boot:run -Dspring-boot.run.arguments=--server.port=8081
  ```

#### 4. Out of memory errors
- Increase JVM memory:
  ```bash
  export MAVEN_OPTS="-Xmx2g"
  mvn spring-boot:run
  ```

#### 5. File upload issues
- Check file size limits in `application.properties`
- Ensure the file format is supported
- Verify file permissions

### Dependency Issues

If you encounter dependency download issues:

1. **Clear Maven cache:**
   ```bash
   mvn dependency:purge-local-repository
   mvn clean install
   ```

2. **Use a different Maven repository:**
   Add to `pom.xml`:
   ```xml
   <repositories>
       <repository>
           <id>central</id>
           <url>https://repo1.maven.org/maven2</url>
       </repository>
   </repositories>
   ```

## Development Setup

### IDE Configuration

#### IntelliJ IDEA:
1. Open the project folder
2. IDEA will automatically detect it as a Maven project
3. Wait for dependency resolution
4. Run the main class: `DocumentChapterExtractorApplication`

#### Eclipse:
1. File → Import → Existing Maven Projects
2. Select the project folder
3. Right-click project → Run As → Spring Boot App

#### VS Code:
1. Install Java Extension Pack
2. Open the project folder
3. Use Command Palette: "Java: Run Spring Boot App"

### Hot Reload (Development)
The application includes Spring Boot DevTools for automatic restart during development:
- Make changes to Java files
- The application will automatically restart
- Frontend changes (HTML/CSS/JS) are reflected immediately

## Production Deployment

### Building for Production
```bash
mvn clean package -Pprod
```

### Running in Production
```bash
java -jar target/document-chapter-extractor-1.0.0.jar \
  --spring.profiles.active=prod \
  --server.port=80
```

### Environment Variables
Set these for production:
```bash
export JAVA_OPTS="-Xmx1g -Xms512m"
export SERVER_PORT=8080
export LOG_LEVEL=WARN
```

## Docker Deployment (Optional)

Create a `Dockerfile`:
```dockerfile
FROM openjdk:17-jre-slim
COPY target/document-chapter-extractor-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

Build and run:
```bash
docker build -t document-extractor .
docker run -p 8080:8080 document-extractor
```

## Testing the Installation

1. **Access the application** at `http://localhost:8080`
2. **Upload a test file** from the `sample-documents/` folder
3. **Verify chapter extraction** works correctly
4. **Download individual chapters** to test the download functionality
5. **Download ZIP file** to test bulk download

## Support

If you encounter issues:
1. Check the console output for error messages
2. Verify all prerequisites are installed correctly
3. Ensure no other applications are using port 8080
4. Check the application logs in the console
5. Try the sample document provided in `sample-documents/`

## Performance Tuning

For large documents:
- Increase JVM heap size: `-Xmx4g`
- Adjust file upload limits in `application.properties`
- Consider using a reverse proxy (nginx) for production

## Security Notes

- The application runs on localhost by default
- For production, configure proper security headers
- Consider adding authentication if needed
- Validate all uploaded files thoroughly
- Use HTTPS in production environments
