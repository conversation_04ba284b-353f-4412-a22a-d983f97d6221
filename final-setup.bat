@echo off
echo.
echo ========================================
echo    Final Setup - Document Extractor
echo ========================================
echo.

REM Kill any hanging processes first
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im mvn.cmd >nul 2>&1

echo 🔍 Checking current setup...
echo.

REM Check Java
echo Testing Java...
"C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe" -version
if %ERRORLEVEL% EQU 0 (
    echo ✅ Java is working
    set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
) else (
    echo ❌ Java not found
    echo Please install Java 17 from https://adoptium.net/
    pause
    exit /b 1
)

echo.

REM Look for Maven on Desktop
echo 🔍 Looking for Maven on Desktop...
set DESKTOP=%USERPROFILE%\Desktop

if exist "%DESKTOP%\apache-maven-3.9.11\bin\mvn.cmd" (
    echo ✅ Found Maven at: %DESKTOP%\apache-maven-3.9.11
    set MAVEN_HOME=%DESKTOP%\apache-maven-3.9.11
    goto :maven_found
)

if exist "%DESKTOP%\apache-maven-3.9.11-bin\apache-maven-3.9.11\bin\mvn.cmd" (
    echo ✅ Found Maven at: %DESKTOP%\apache-maven-3.9.11-bin\apache-maven-3.9.11
    set MAVEN_HOME=%DESKTOP%\apache-maven-3.9.11-bin\apache-maven-3.9.11
    goto :maven_found
)

REM Look for any Maven folder
for /d %%i in ("%DESKTOP%\*maven*") do (
    if exist "%%i\bin\mvn.cmd" (
        echo ✅ Found Maven at: %%i
        set MAVEN_HOME=%%i
        goto :maven_found
    )
    REM Check if it's a zip extraction folder
    for /d %%j in ("%%i\apache-maven*") do (
        if exist "%%j\bin\mvn.cmd" (
            echo ✅ Found Maven at: %%j
            set MAVEN_HOME=%%j
            goto :maven_found
        )
    )
)

echo ❌ Maven not found on Desktop
echo.
echo 📋 Please download Maven from: https://maven.apache.org/download.cgi
echo    Extract it to Desktop and run this script again
pause
exit /b 1

:maven_found
echo Maven Home: %MAVEN_HOME%
echo.

REM Set environment for current session
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%

REM Test Maven
echo 🧪 Testing Maven...
"%MAVEN_HOME%\bin\mvn.cmd" --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Maven test failed
    pause
    exit /b 1
)

echo ✅ Maven is working
echo.

REM Navigate to project
if not exist "document-chapter-extractor" (
    echo ❌ Document Chapter Extractor project not found
    echo Current directory: %CD%
    pause
    exit /b 1
)

cd document-chapter-extractor
echo 📁 Changed to project directory
echo.

REM Build and run
echo 🔨 Building application...
"%MAVEN_HOME%\bin\mvn.cmd" clean compile -DskipTests -q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed
    echo.
    echo 💡 Trying with verbose output...
    "%MAVEN_HOME%\bin\mvn.cmd" clean compile -DskipTests
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🚀 Starting Document Chapter Extractor...
echo 📍 Application will be available at: http://localhost:8080
echo ⏹️  Press Ctrl+C to stop
echo.

REM Open browser after delay
start /b timeout /t 10 /nobreak >nul && start http://localhost:8080

REM Start the application
"%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run
