# Maven Installation Script for Windows
Write-Host "🚀 Installing <PERSON><PERSON> from Desktop..." -ForegroundColor Green
Write-Host ""

# Set variables
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$MavenSource = Join-Path $DesktopPath "maven-mvnd-1.0.2-windows-amd64"
$MavenHome = "C:\Program Files\Apache\Maven"
$MavenBin = Join-Path $MavenHome "bin"

Write-Host "📁 Source folder: $MavenSource" -ForegroundColor Cyan
Write-Host "📁 Target folder: $MavenHome" -ForegroundColor Cyan
Write-Host ""

# Check if source folder exists
if (-not (Test-Path $MavenSource)) {
    Write-Host "❌ Error: Maven folder not found at $MavenSource" -ForegroundColor Red
    Write-Host "Please make sure the maven-mvnd-1.0.2-windows-amd64 folder is on your Desktop" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found Maven folder on Desktop" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  This script needs administrator privileges to install Maven to Program Files" -ForegroundColor Yellow
    Write-Host "🔄 Attempting to restart as administrator..." -ForegroundColor Cyan
    
    try {
        Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
        exit
    }
    catch {
        Write-Host "❌ Failed to restart as administrator. Please run PowerShell as Administrator manually." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host "✅ Running with administrator privileges" -ForegroundColor Green
Write-Host ""

try {
    # Create Apache directory if it doesn't exist
    $ApacheDir = "C:\Program Files\Apache"
    if (-not (Test-Path $ApacheDir)) {
        Write-Host "📂 Creating Apache directory..." -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $ApacheDir -Force | Out-Null
    }

    # Copy Maven to Program Files
    Write-Host "📋 Copying Maven files to Program Files..." -ForegroundColor Cyan
    if (Test-Path $MavenHome) {
        Write-Host "🗑️  Removing existing Maven installation..." -ForegroundColor Yellow
        Remove-Item $MavenHome -Recurse -Force
    }
    
    Copy-Item $MavenSource $MavenHome -Recurse -Force
    Write-Host "✅ Maven files copied successfully" -ForegroundColor Green
    Write-Host ""

    # Set MAVEN_HOME environment variable
    Write-Host "🔧 Setting MAVEN_HOME environment variable..." -ForegroundColor Cyan
    [Environment]::SetEnvironmentVariable("MAVEN_HOME", $MavenHome, [EnvironmentVariableTarget]::Machine)
    
    # Add Maven to PATH
    Write-Host "🔧 Adding Maven to system PATH..." -ForegroundColor Cyan
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
    
    if ($currentPath -notlike "*$MavenBin*") {
        $newPath = $currentPath + ";" + $MavenBin
        [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
        Write-Host "✅ Maven added to system PATH" -ForegroundColor Green
    } else {
        Write-Host "✅ Maven is already in system PATH" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "✅ Maven installation completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Installation Summary:" -ForegroundColor Cyan
    Write-Host "   Maven Home: $MavenHome" -ForegroundColor White
    Write-Host "   Maven Bin:  $MavenBin" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 Please restart your command prompt or PowerShell to use Maven" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 To test Maven installation, open a new command prompt and run:" -ForegroundColor Cyan
    Write-Host "   mvn --version" -ForegroundColor White
    Write-Host ""

} catch {
    Write-Host "❌ Error during installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "   1. Make sure you're running as Administrator" -ForegroundColor White
    Write-Host "   2. Check if antivirus is blocking the operation" -ForegroundColor White
    Write-Host "   3. Ensure the Maven folder exists on Desktop" -ForegroundColor White
}

Read-Host "Press Enter to exit"
