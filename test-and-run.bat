@echo off
echo.
echo ========================================
echo   Document Chapter Extractor Launcher
echo ========================================
echo.

REM Test Java installation
echo 🧪 Testing Java installation...
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java is not installed or not in PATH
    echo.
    echo 💡 Please install Java 17 first:
    echo    1. Go to https://adoptium.net/
    echo    2. Download Java 17 ^(LTS^)
    echo    3. Install and restart command prompt
    echo.
    pause
    exit /b 1
)
echo ✅ Java is installed

REM Test Maven installation
echo 🧪 Testing Maven installation...
mvn --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Maven is not installed or not in PATH
    echo.
    echo 💡 Maven should be installed at: %USERPROFILE%\maven
    echo    Please restart your command prompt and try again
    echo.
    pause
    exit /b 1
)
echo ✅ Maven is installed

echo.
echo ✅ All prerequisites are ready!
echo.

REM Navigate to project directory
if not exist "document-chapter-extractor" (
    echo ❌ Document Chapter Extractor project not found
    echo    Please make sure you're in the correct directory
    pause
    exit /b 1
)

cd document-chapter-extractor
echo 📁 Changed to project directory

echo.
echo 🔨 Building the application...
echo    This may take a few minutes for the first build...
echo.

mvn clean install
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 🚀 Starting the Document Chapter Extractor...
echo.
echo 📍 The application will be available at: http://localhost:8080
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start the application
mvn spring-boot:run
