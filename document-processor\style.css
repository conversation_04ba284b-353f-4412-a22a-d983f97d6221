/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #cbd5e1;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

[data-theme="dark"] {
    /* Dark Theme Colors */
    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --border-color: #475569;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.header-content h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.header-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Step Sections */
.step-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.step-header {
    margin-bottom: 2rem;
}

.step-header h2 {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.step-number {
    background: var(--primary-color);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 700;
}

.step-header p {
    color: var(--text-secondary);
    margin-left: 3rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background: var(--bg-secondary);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.upload-content i {
    font-size: 3rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.upload-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.upload-content p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.file-types {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* File Preview */
.upload-preview {
    text-align: left;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.file-info i {
    font-size: 2rem;
    color: var(--primary-color);
}

.file-details h4 {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.file-details p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.processing-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

/* Chapter Actions */
.chapter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 1rem;
}

.chapter-stats-summary {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Chapter Browser */
.chapter-browser {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: 500px;
}

.chapter-list-container {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    background: var(--bg-secondary);
}

.chapter-list-container h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.chapter-search {
    position: relative;
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.chapter-search i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.chapter-list {
    max-height: 400px;
    overflow-y: auto;
    display: grid;
    gap: 0.75rem;
}

.chapter-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    background: var(--bg-primary);
    position: relative;
}

.chapter-item:hover {
    border-color: var(--primary-color);
    background: var(--bg-tertiary);
}

.chapter-item.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.chapter-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.chapter-item-title {
    flex: 1;
    cursor: pointer;
}

.chapter-item h4 {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.chapter-item p {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

.chapter-item-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.chapter-checkbox {
    margin-right: 0.5rem;
}

.chapter-download-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: auto;
}

.bulk-actions {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
}

.chapter-details {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    background: var(--bg-secondary);
}

.chapter-details h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.page-selector {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.page-selector h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.page-options {
    display: grid;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-mark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    margin-right: 0.75rem;
    position: relative;
    transition: var(--transition);
}

.radio-option input[type="radio"]:checked + .radio-mark {
    border-color: var(--primary-color);
}

.radio-option input[type="radio"]:checked + .radio-mark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.page-range-controls,
.custom-pages-controls {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.range-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
}

.input-group input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.custom-pages-controls input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    margin-top: 0.5rem;
}

.chapter-preview {
    margin-top: 1.5rem;
}

.chapter-preview h4 {
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

/* Chapter Preview */
.chapter-preview {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chapter-stats {
    display: flex;
    gap: 2rem;
    margin: 1rem 0;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.chapter-text {
    max-height: 200px;
    overflow-y: auto;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Grouping Controls */
.grouping-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
    margin-bottom: 2rem;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: var(--text-primary);
}

.control-group input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
}

.help-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Page Groups */
.page-groups {
    display: grid;
    gap: 1rem;
}

.page-group {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-secondary);
}

.page-group-header {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-group-header h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.page-group-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.page-group-content {
    padding: 1rem;
    max-height: 150px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-small {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
}

.btn-icon {
    padding: 0.5rem;
    width: 40px;
    height: 40px;
}

/* Export Controls */
.export-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.export-options {
    display: flex;
    gap: 1rem;
}

.export-settings {
    display: flex;
    gap: 1rem;
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Preview */
.preview-container {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-secondary);
}

.preview-navigation {
    background: var(--bg-tertiary);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.group-indicator {
    font-weight: 500;
    color: var(--text-primary);
}

.preview-content {
    padding: 2rem;
    min-height: 300px;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* Status Bar */
.status-bar {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: 0.75rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 50px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .step-section {
        padding: 1.5rem;
    }

    .chapter-browser {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .range-inputs {
        grid-template-columns: 1fr;
    }

    .chapter-actions {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .chapter-item-header {
        flex-direction: column;
        align-items: stretch;
    }

    .chapter-item-actions {
        justify-content: center;
        margin-top: 0.5rem;
    }

    .export-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .export-options {
        justify-content: center;
    }
}
