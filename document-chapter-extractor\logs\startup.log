[INFO] Scanning for projects...
[INFO] 
[INFO] ----------< com.documentprocessor:document-chapter-extractor >----------
[INFO] Building Document Chapter Extractor 1.0.0
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] >>> spring-boot:3.2.0:run (default-cli) > test-compile @ document-chapter-extractor >>>
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ document-chapter-extractor ---
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] Copying 3 resources from src\main\resources to target\classes
[INFO] 
[INFO] --- compiler:3.11.0:compile (default-compile) @ document-chapter-extractor ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ document-chapter-extractor ---
[INFO] skip non existing resourceDirectory C:\Users\<USER>\Desktop\test\document-chapter-extractor\src\test\resources
[INFO] 
[INFO] --- compiler:3.11.0:testCompile (default-testCompile) @ document-chapter-extractor ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] <<< spring-boot:3.2.0:run (default-cli) < test-compile @ document-chapter-extractor <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:3.2.0:run (default-cli) @ document-chapter-extractor ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-23 01:35:12 [restartedMain] INFO  c.d.DocumentChapterExtractorApplication - Starting DocumentChapterExtractorApplication using Java 17.0.16 with PID 30876 (C:\Users\<USER>\Desktop\test\document-chapter-extractor\target\classes started by Prissy in C:\Users\<USER>\Desktop\test\document-chapter-extractor)
2025-07-23 01:35:12 [restartedMain] DEBUG c.d.DocumentChapterExtractorApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-23 01:35:12 [restartedMain] INFO  c.d.DocumentChapterExtractorApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-23 01:35:12 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-23 01:35:12 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-23 01:35:13 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-23 01:35:13 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 01:35:13 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-07-23 01:35:13 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 01:35:13 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1592 ms
2025-07-23 01:35:13 [restartedMain] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page template: index
2025-07-23 01:35:13 [restartedMain] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 8 mappings in 'requestMappingHandlerMapping'
2025-07-23 01:35:13 [restartedMain] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-23 01:35:14 [restartedMain] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-23 01:35:14 [restartedMain] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-23 01:35:14 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-07-23 01:35:14 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-07-23 01:35:14 [restartedMain] INFO  c.d.DocumentChapterExtractorApplication - Started DocumentChapterExtractorApplication in 2.607 seconds (process running for 3.286)
