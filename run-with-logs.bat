@echo off
echo Starting Document Chapter Extractor with detailed logging...
echo.

REM Set environment
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set MAVEN_HOME=C:\Users\<USER>\maven
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\mvn\bin;%PATH%

cd document-chapter-extractor

echo Creating logs directory...
if not exist logs mkdir logs

echo Starting application...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" spring-boot:run > logs\startup.log 2>&1

echo Application started. Check logs\startup.log for details.
pause
