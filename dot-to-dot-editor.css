/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    color: #4a5568;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.app-header h1 i {
    color: #667eea;
    margin-right: 0.5rem;
}

.app-header p {
    color: #718096;
    font-size: 1rem;
}

/* Main Content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 1rem;
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Left Panel */
.left-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: fit-content;
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.left-panel section {
    margin-bottom: 2rem;
}

.left-panel h3 {
    color: #4a5568;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.left-panel h3 i {
    color: #667eea;
}

/* Upload Section */
.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #f7fafc;
}

.upload-area:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e6fffa;
    transform: scale(1.02);
}

.upload-content i {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.upload-content p {
    color: #4a5568;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.upload-subtitle {
    color: #718096;
    font-size: 0.9rem;
}

.upload-preview {
    position: relative;
}

.upload-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.remove-image {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-image:hover {
    background: #c53030;
    transform: scale(1.1);
}

.supported-formats {
    margin-top: 0.5rem;
    text-align: center;
}

.supported-formats small {
    color: #718096;
}

/* Control Groups */
.control-group {
    margin-bottom: 1.5rem;
}

.control-group label {
    display: block;
    color: #4a5568;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Toggle Group */
.toggle-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.toggle-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
    cursor: pointer;
}

.toggle-item label {
    color: #4a5568;
    cursor: pointer;
    margin: 0;
}

/* Buttons */
.btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
    transform: translateY(-1px);
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c53030;
    transform: translateY(-1px);
}

.btn-export {
    background: #38a169;
    color: white;
    flex: 1;
}

.btn-export:hover:not(:disabled) {
    background: #2f855a;
    transform: translateY(-1px);
}

.btn-icon {
    padding: 0.5rem;
    background: #f7fafc;
    color: #4a5568;
    border-radius: 6px;
}

.btn-icon:hover:not(:disabled) {
    background: #e2e8f0;
}

/* Button Layouts */
.button-grid {
    display: grid;
    gap: 0.75rem;
}

.export-buttons {
    display: flex;
    gap: 0.5rem;
}

/* Status Section */
.status-message {
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 1rem;
    display: none;
}

.status-message.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
    display: block;
}

.status-message.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #fc8181;
    display: block;
}

.status-message.info {
    background: #bee3f8;
    color: #2a4365;
    border: 1px solid #90cdf4;
    display: block;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    width: 0%;
}

/* Right Panel */
.right-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.canvas-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.canvas-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoom-level {
    font-weight: 500;
    color: #4a5568;
    min-width: 50px;
    text-align: center;
}

.canvas-info {
    color: #718096;
    font-size: 0.9rem;
}

.undo-redo {
    display: flex;
    gap: 0.5rem;
}

.canvas-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: #f7fafc;
}

.background-canvas,
.dots-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: crosshair;
}

.canvas-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #a0aec0;
}

.canvas-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.canvas-placeholder p {
    font-size: 1.1rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 300px 1fr;
    }
    
    .left-panel {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .left-panel {
        max-height: none;
        order: 2;
    }
    
    .right-panel {
        order: 1;
        min-height: 400px;
    }
    
    .app-header {
        padding: 1rem;
    }
    
    .app-header h1 {
        font-size: 1.5rem;
    }
    
    .export-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.5rem;
    }
    
    .canvas-controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .zoom-controls,
    .undo-redo {
        justify-content: center;
    }
}
