@echo off
echo.
echo ========================================
echo    Installing Apache Maven 3.9.11
echo ========================================
echo.

REM Look for Maven folder on Desktop
set DESKTOP=%USERPROFILE%\Desktop
set MAVEN_SOURCE=

echo 🔍 Looking for Maven folder on Desktop...

if exist "%DESKTOP%\apache-maven-3.9.11" (
    set MAVEN_SOURCE=%DESKTOP%\apache-maven-3.9.11
    echo ✅ Found: apache-maven-3.9.11
    goto :found
)

if exist "%DESKTOP%\apache-maven-3.9.11-bin" (
    set MAVEN_SOURCE=%DESKTOP%\apache-maven-3.9.11-bin
    echo ✅ Found: apache-maven-3.9.11-bin
    goto :found
)

REM Look for any folder containing maven
for /d %%i in ("%DESKTOP%\*maven*") do (
    set MAVEN_SOURCE=%%i
    echo ✅ Found Maven folder: %%i
    goto :found
)

echo ❌ No Maven folder found on Desktop
echo.
echo 📋 Folders on Desktop:
dir "%DESKTOP%" /b /ad
echo.
echo Please make sure the apache-maven-3.9.11 folder is on your Desktop
pause
exit /b 1

:found
echo Maven source: %MAVEN_SOURCE%
echo.

REM Set target directory
set MAVEN_HOME=C:\Program Files\Apache\Maven
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot

echo 📁 Installing to: %MAVEN_HOME%
echo.

REM Create Apache directory
if not exist "C:\Program Files\Apache" (
    echo 📂 Creating Apache directory...
    mkdir "C:\Program Files\Apache"
)

REM Remove existing Maven if any
if exist "%MAVEN_HOME%" (
    echo 🗑️  Removing existing Maven...
    rmdir /s /q "%MAVEN_HOME%"
)

REM Copy Maven files
echo 📋 Copying Maven files...
xcopy "%MAVEN_SOURCE%" "%MAVEN_HOME%" /E /I /Y /Q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to copy Maven files
    echo 💡 Try running this script as Administrator
    pause
    exit /b 1
)

echo ✅ Maven files copied successfully
echo.

REM Set environment variables
echo 🔧 Setting environment variables...

REM Set JAVA_HOME
setx JAVA_HOME "%JAVA_HOME%" /M >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Setting JAVA_HOME as user variable...
    setx JAVA_HOME "%JAVA_HOME%" >nul 2>&1
)

REM Set MAVEN_HOME
setx MAVEN_HOME "%MAVEN_HOME%" /M >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Setting MAVEN_HOME as user variable...
    setx MAVEN_HOME "%MAVEN_HOME%" >nul 2>&1
)

REM Update PATH
echo 🔧 Updating PATH...
setx PATH "%PATH%;%MAVEN_HOME%\bin" /M >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  Updating PATH as user variable...
    setx PATH "%PATH%;%MAVEN_HOME%\bin" >nul 2>&1
)

echo.
echo ✅ Maven installation completed!
echo.
echo 📋 Installation Summary:
echo    Java Home:  %JAVA_HOME%
echo    Maven Home: %MAVEN_HOME%
echo    Maven Bin:  %MAVEN_HOME%\bin
echo.

REM Test the installation
echo 🧪 Testing Maven installation...
echo.

REM Set environment for current session
set JAVA_HOME=%JAVA_HOME%
set MAVEN_HOME=%MAVEN_HOME%
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%

REM Test Maven
"%MAVEN_HOME%\bin\mvn.cmd" --version

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Maven is working correctly!
    echo.
    echo 🚀 Ready to run Document Chapter Extractor!
    echo.
    echo 📋 Next steps:
    echo    1. Open a NEW command prompt
    echo    2. cd "%~dp0document-chapter-extractor"
    echo    3. mvn clean install
    echo    4. mvn spring-boot:run
    echo    5. Open browser to: http://localhost:8080
    echo.
    
    set /p choice="Would you like to run the application now? (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 Starting Document Chapter Extractor...
        cd "%~dp0document-chapter-extractor"
        "%MAVEN_HOME%\bin\mvn.cmd" clean install
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo 🌐 Starting server at http://localhost:8080...
            start http://localhost:8080
            "%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run
        )
    )
) else (
    echo.
    echo ⚠️  Maven test failed. Please restart your command prompt and try again.
)

echo.
pause
