/**
 * Document Processor - Chapter & Page Grouping Tool
 * Complete web application for processing documents and grouping content
 */

class DocumentProcessor {
    constructor() {
        // Core properties
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.selectedChapter = null;
        this.pageGroups = [];
        this.currentGroupIndex = 0;
        
        // Settings
        this.settings = {
            pagesPerGroup: 2,
            wordsPerPage: 300,
            includeHeaders: true,
            includeNumbers: true
        };
        
        // DOM elements
        this.elements = {};
        
        this.init();
    }
    
    init() {
        try {
            this.setupElements();
            this.setupEventListeners();
            console.log('DocumentProcessor initialized successfully');
        } catch (error) {
            console.error('Error initializing DocumentProcessor:', error);
        }
    }
    
    setupElements() {
        // Get all required DOM elements
        const elementIds = [
            'uploadArea', 'fileInput', 'uploadPreview', 'fileName', 'fileSize', 'fileType',
            'removeFile', 'processingStatus', 'chapterSection', 'chapterList', 'chapterPreview',
            'selectedChapterTitle', 'chapterWords', 'chapterPages', 'chapterText',
            'groupingSection', 'pagesPerGroup', 'wordsPerPage', 'generateGroups', 'pageGroups',
            'exportSection', 'exportPDF', 'exportTXT', 'exportJSON', 'includeHeaders',
            'includeNumbers', 'previewContainer', 'prevGroup', 'nextGroup', 'groupIndicator',
            'previewContent', 'statusMessage', 'progressContainer', 'progressFill',
            'progressText', 'loadingOverlay', 'loadingText', 'darkModeToggle'
        ];
        
        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
            if (!this.elements[id]) {
                console.error(`Element not found: ${id}`);
            }
        });
    }
    
    setupEventListeners() {
        // File upload
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('dragover');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', () => {
            this.elements.uploadArea.classList.remove('dragover');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });
        
        // Remove file
        this.elements.removeFile.addEventListener('click', () => {
            this.removeFile();
        });
        
        // Settings
        this.elements.pagesPerGroup.addEventListener('input', (e) => {
            this.settings.pagesPerGroup = parseInt(e.target.value);
        });
        
        this.elements.wordsPerPage.addEventListener('input', (e) => {
            this.settings.wordsPerPage = parseInt(e.target.value);
        });
        
        this.elements.includeHeaders.addEventListener('change', (e) => {
            this.settings.includeHeaders = e.target.checked;
        });
        
        this.elements.includeNumbers.addEventListener('change', (e) => {
            this.settings.includeNumbers = e.target.checked;
        });
        
        // Action buttons
        this.elements.generateGroups.addEventListener('click', () => {
            this.generatePageGroups();
        });
        
        // Export buttons
        this.elements.exportPDF.addEventListener('click', () => {
            this.exportAsPDF();
        });
        
        this.elements.exportTXT.addEventListener('click', () => {
            this.exportAsText();
        });
        
        this.elements.exportJSON.addEventListener('click', () => {
            this.exportAsJSON();
        });
        
        // Navigation
        this.elements.prevGroup.addEventListener('click', () => {
            this.navigateGroup(-1);
        });
        
        this.elements.nextGroup.addEventListener('click', () => {
            this.navigateGroup(1);
        });
        
        // Dark mode toggle
        this.elements.darkModeToggle.addEventListener('click', () => {
            this.toggleDarkMode();
        });
    }
    
    // File handling methods
    handleFileSelect(files) {
        if (files && files.length > 0) {
            this.loadFile(files[0]);
        }
    }
    
    loadFile(file) {
        const validTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
        
        if (!validTypes.includes(file.type)) {
            this.showStatus('Please select a valid PDF, DOCX, or TXT file', 'error');
            return;
        }
        
        this.currentFile = file;
        this.showFilePreview(file);
        this.processFile(file);
    }
    
    showFilePreview(file) {
        // Show file info
        this.elements.fileName.textContent = file.name;
        this.elements.fileSize.textContent = this.formatFileSize(file.size);
        this.elements.fileType.textContent = this.getFileTypeLabel(file.type);
        
        // Show preview section
        this.elements.uploadPreview.style.display = 'block';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'none';
        
        // Show processing status
        this.elements.processingStatus.style.display = 'flex';
    }
    
    async processFile(file) {
        this.showLoading(true, 'Processing document...');
        
        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('file', file);
            
            // Send to backend for processing
            const response = await fetch('/api/process-document', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.extractedText = result.text;
                this.chapters = result.chapters;
                this.showChapterSelection();
                this.showStatus('Document processed successfully', 'success');
            } else {
                throw new Error(result.error || 'Failed to process document');
            }
            
        } catch (error) {
            console.error('Error processing file:', error);
            this.showStatus('Failed to process document. Please try again.', 'error');
        } finally {
            this.showLoading(false);
            this.elements.processingStatus.style.display = 'none';
        }
    }
    
    showChapterSelection() {
        // Show chapter section
        this.elements.chapterSection.style.display = 'block';
        
        // Populate chapter list
        const chapterList = this.elements.chapterList;
        chapterList.innerHTML = '';
        
        Object.keys(this.chapters).forEach(chapterTitle => {
            const chapterItem = document.createElement('div');
            chapterItem.className = 'chapter-item';
            chapterItem.innerHTML = `
                <h4>${chapterTitle}</h4>
                <p>${this.chapters[chapterTitle].substring(0, 100)}...</p>
            `;
            
            chapterItem.addEventListener('click', () => {
                this.selectChapter(chapterTitle);
            });
            
            chapterList.appendChild(chapterItem);
        });
    }
    
    selectChapter(chapterTitle) {
        this.selectedChapter = chapterTitle;
        
        // Update UI
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.target.closest('.chapter-item').classList.add('selected');
        
        // Show chapter preview
        const chapterText = this.chapters[chapterTitle];
        const wordCount = chapterText.split(/\s+/).length;
        const estimatedPages = Math.ceil(wordCount / this.settings.wordsPerPage);
        
        this.elements.selectedChapterTitle.textContent = chapterTitle;
        this.elements.chapterWords.textContent = wordCount.toLocaleString();
        this.elements.chapterPages.textContent = estimatedPages;
        this.elements.chapterText.textContent = chapterText.substring(0, 500) + '...';
        
        this.elements.chapterPreview.style.display = 'block';
        this.elements.groupingSection.style.display = 'block';
    }
    
    generatePageGroups() {
        if (!this.selectedChapter) {
            this.showStatus('Please select a chapter first', 'error');
            return;
        }
        
        const chapterText = this.chapters[this.selectedChapter];
        const words = chapterText.split(/\s+/);
        const wordsPerGroup = this.settings.wordsPerPage * this.settings.pagesPerGroup;
        
        this.pageGroups = [];
        
        for (let i = 0; i < words.length; i += wordsPerGroup) {
            const groupWords = words.slice(i, i + wordsPerGroup);
            const groupText = groupWords.join(' ');
            
            const groupNumber = Math.floor(i / wordsPerGroup) + 1;
            const startPage = (groupNumber - 1) * this.settings.pagesPerGroup + 1;
            const endPage = Math.min(startPage + this.settings.pagesPerGroup - 1, 
                                   Math.ceil(words.length / this.settings.wordsPerPage));
            
            this.pageGroups.push({
                groupNumber,
                pages: `${startPage}-${endPage}`,
                text: groupText,
                wordCount: groupWords.length
            });
        }
        
        this.showPageGroups();
        this.elements.exportSection.style.display = 'block';
        this.showPreview();
    }
    
    showPageGroups() {
        const pageGroupsContainer = this.elements.pageGroups;
        pageGroupsContainer.innerHTML = '';
        
        this.pageGroups.forEach(group => {
            const groupElement = document.createElement('div');
            groupElement.className = 'page-group';
            groupElement.innerHTML = `
                <div class="page-group-header">
                    <h4>Group ${group.groupNumber} (Pages ${group.pages})</h4>
                    <div class="page-group-stats">
                        <span><i class="fas fa-align-left"></i> ${group.wordCount} words</span>
                    </div>
                </div>
                <div class="page-group-content">
                    ${group.text.substring(0, 200)}...
                </div>
            `;
            
            pageGroupsContainer.appendChild(groupElement);
        });
        
        pageGroupsContainer.style.display = 'block';
    }
    
    showPreview() {
        this.currentGroupIndex = 0;
        this.updatePreview();
        this.updateNavigationButtons();
    }
    
    updatePreview() {
        if (this.pageGroups.length === 0) return;
        
        const group = this.pageGroups[this.currentGroupIndex];
        let content = '';
        
        if (this.settings.includeHeaders) {
            content += `=== ${this.selectedChapter} ===\n`;
            content += `Group ${group.groupNumber} (Pages ${group.pages})\n\n`;
        }
        
        content += group.text;
        
        if (this.settings.includeNumbers) {
            content += `\n\n--- Page Group ${group.groupNumber} ---`;
        }
        
        this.elements.previewContent.textContent = content;
        this.elements.groupIndicator.textContent = `Group ${this.currentGroupIndex + 1} of ${this.pageGroups.length}`;
    }
    
    navigateGroup(direction) {
        this.currentGroupIndex += direction;
        this.currentGroupIndex = Math.max(0, Math.min(this.currentGroupIndex, this.pageGroups.length - 1));
        this.updatePreview();
        this.updateNavigationButtons();
    }
    
    updateNavigationButtons() {
        this.elements.prevGroup.disabled = this.currentGroupIndex === 0;
        this.elements.nextGroup.disabled = this.currentGroupIndex === this.pageGroups.length - 1;
    }

    // Export methods
    exportAsPDF() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;
            const lineHeight = 6;

            this.pageGroups.forEach((group, index) => {
                // Add new page if not the first group
                if (index > 0) {
                    doc.addPage();
                    yPosition = 20;
                }

                // Add header if enabled
                if (this.settings.includeHeaders) {
                    doc.setFontSize(16);
                    doc.setFont(undefined, 'bold');
                    doc.text(`${this.selectedChapter}`, margin, yPosition);
                    yPosition += 10;

                    doc.setFontSize(12);
                    doc.setFont(undefined, 'normal');
                    doc.text(`Group ${group.groupNumber} (Pages ${group.pages})`, margin, yPosition);
                    yPosition += 15;
                }

                // Add content
                doc.setFontSize(10);
                const lines = doc.splitTextToSize(group.text, doc.internal.pageSize.width - 2 * margin);

                lines.forEach(line => {
                    if (yPosition > pageHeight - margin) {
                        doc.addPage();
                        yPosition = margin;
                    }
                    doc.text(line, margin, yPosition);
                    yPosition += lineHeight;
                });

                // Add page numbers if enabled
                if (this.settings.includeNumbers) {
                    yPosition += 10;
                    doc.setFontSize(8);
                    doc.text(`--- Page Group ${group.groupNumber} ---`, margin, yPosition);
                }
            });

            // Download the PDF
            const fileName = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.pdf`;
            doc.save(fileName);

            this.showStatus('PDF exported successfully', 'success');

        } catch (error) {
            console.error('Error exporting PDF:', error);
            this.showStatus('Failed to export PDF', 'error');
        }
    }

    exportAsText() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        let content = '';

        this.pageGroups.forEach(group => {
            if (this.settings.includeHeaders) {
                content += `=== ${this.selectedChapter} ===\n`;
                content += `Group ${group.groupNumber} (Pages ${group.pages})\n\n`;
            }

            content += group.text;

            if (this.settings.includeNumbers) {
                content += `\n\n--- Page Group ${group.groupNumber} ---\n\n`;
            } else {
                content += '\n\n';
            }

            content += '\n' + '='.repeat(50) + '\n\n';
        });

        // Download as text file
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.txt`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus('Text file exported successfully', 'success');
    }

    exportAsJSON() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        const exportData = {
            document: this.currentFile ? this.currentFile.name : 'Unknown',
            chapter: this.selectedChapter,
            settings: this.settings,
            groups: this.pageGroups.map(group => ({
                groupNumber: group.groupNumber,
                pages: group.pages,
                wordCount: group.wordCount,
                text: group.text
            })),
            exportDate: new Date().toISOString()
        };

        // Download as JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.json`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus('JSON file exported successfully', 'success');
    }

    // Utility methods
    removeFile() {
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.selectedChapter = null;
        this.pageGroups = [];

        // Reset UI
        this.elements.uploadPreview.style.display = 'none';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'block';
        this.elements.chapterSection.style.display = 'none';
        this.elements.groupingSection.style.display = 'none';
        this.elements.exportSection.style.display = 'none';

        this.showStatus('File removed', 'info');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileTypeLabel(mimeType) {
        const types = {
            'application/pdf': 'PDF Document',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
            'text/plain': 'Text File'
        };
        return types[mimeType] || 'Unknown';
    }

    showStatus(message, type = 'info') {
        this.elements.statusMessage.textContent = message;
        this.elements.statusMessage.className = `status-message ${type}`;

        // Auto-clear after 3 seconds
        setTimeout(() => {
            this.elements.statusMessage.textContent = '';
            this.elements.statusMessage.className = 'status-message';
        }, 3000);
    }

    showLoading(show, text = 'Processing...') {
        this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        if (text) {
            this.elements.loadingText.textContent = text;
        }
    }

    toggleDarkMode() {
        const body = document.body;
        const isDark = body.getAttribute('data-theme') === 'dark';

        if (isDark) {
            body.removeAttribute('data-theme');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            body.setAttribute('data-theme', 'dark');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentProcessor();
});
