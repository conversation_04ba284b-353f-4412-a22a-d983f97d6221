/**
 * Document Processor - Chapter & Page Grouping Tool
 * Complete web application for processing documents and grouping content
 */

class DocumentProcessor {
    constructor() {
        // Core properties
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.selectedChapter = null;
        this.selectedPages = null;
        this.pageGroups = [];
        this.currentGroupIndex = 0;
        this.chapterPages = {};
        
        // Settings
        this.settings = {
            pagesPerGroup: 2,
            wordsPerPage: 300,
            includeHeaders: true,
            includeNumbers: true
        };
        
        // DOM elements
        this.elements = {};
        
        this.init();
    }
    
    init() {
        try {
            this.setupElements();
            this.setupEventListeners();
            console.log('DocumentProcessor initialized successfully');
        } catch (error) {
            console.error('Error initializing DocumentProcessor:', error);
        }
    }
    
    setupElements() {
        // Get all required DOM elements
        const elementIds = [
            'uploadArea', 'fileInput', 'uploadPreview', 'fileName', 'fileSize', 'fileType',
            'removeFile', 'processingStatus', 'chapterSection', 'chapterSearch', 'chapterList',
            'chapterDetails', 'selectedChapterTitle', 'chapterWords', 'chapterPages', 'chapterText',
            'startPage', 'endPage', 'customPages', 'pageRangeControls', 'customPagesControls',
            'processChapterBtn', 'downloadAllChapters', 'createCombinedPDF', 'totalChapters',
            'totalWords', 'totalPages', 'selectAllChapters', 'downloadSelectedChapters',
            'groupingSection', 'pagesPerGroup', 'wordsPerPage', 'generateGroups',
            'pageGroups', 'exportSection', 'exportPDF', 'exportTXT', 'exportJSON', 'includeHeaders',
            'includeNumbers', 'previewContainer', 'prevGroup', 'nextGroup', 'groupIndicator',
            'previewContent', 'statusMessage', 'progressContainer', 'progressFill',
            'progressText', 'loadingOverlay', 'loadingText', 'darkModeToggle'
        ];
        
        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
            if (!this.elements[id]) {
                console.error(`Element not found: ${id}`);
            }
        });
    }
    
    setupEventListeners() {
        // File upload
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('dragover');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', () => {
            this.elements.uploadArea.classList.remove('dragover');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });
        
        // Remove file
        this.elements.removeFile.addEventListener('click', () => {
            this.removeFile();
        });
        
        // Settings
        this.elements.pagesPerGroup.addEventListener('input', (e) => {
            this.settings.pagesPerGroup = parseInt(e.target.value);
        });
        
        this.elements.wordsPerPage.addEventListener('input', (e) => {
            this.settings.wordsPerPage = parseInt(e.target.value);
        });
        
        this.elements.includeHeaders.addEventListener('change', (e) => {
            this.settings.includeHeaders = e.target.checked;
        });
        
        this.elements.includeNumbers.addEventListener('change', (e) => {
            this.settings.includeNumbers = e.target.checked;
        });
        
        // Chapter search
        this.elements.chapterSearch.addEventListener('input', (e) => {
            this.filterChapters(e.target.value);
        });

        // Page selection radio buttons
        document.querySelectorAll('input[name="pageSelection"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.handlePageSelectionChange(e.target.value);
            });
        });

        // Page range inputs
        this.elements.startPage.addEventListener('input', () => {
            this.updatePagePreview();
        });

        this.elements.endPage.addEventListener('input', () => {
            this.updatePagePreview();
        });

        this.elements.customPages.addEventListener('input', () => {
            this.updatePagePreview();
        });

        // Process chapter button
        this.elements.processChapterBtn.addEventListener('click', () => {
            this.processSelectedChapter();
        });

        // Chapter download buttons
        this.elements.downloadAllChapters.addEventListener('click', () => {
            this.downloadAllChapters();
        });

        this.elements.createCombinedPDF.addEventListener('click', () => {
            this.createCombinedPDF();
        });

        this.elements.selectAllChapters.addEventListener('change', (e) => {
            this.toggleAllChapters(e.target.checked);
        });

        this.elements.downloadSelectedChapters.addEventListener('click', () => {
            this.downloadSelectedChapters();
        });

        // Action buttons
        this.elements.generateGroups.addEventListener('click', () => {
            this.generatePageGroups();
        });
        
        // Export buttons
        this.elements.exportPDF.addEventListener('click', () => {
            this.exportAsPDF();
        });
        
        this.elements.exportTXT.addEventListener('click', () => {
            this.exportAsText();
        });
        
        this.elements.exportJSON.addEventListener('click', () => {
            this.exportAsJSON();
        });
        
        // Navigation
        this.elements.prevGroup.addEventListener('click', () => {
            this.navigateGroup(-1);
        });
        
        this.elements.nextGroup.addEventListener('click', () => {
            this.navigateGroup(1);
        });
        
        // Dark mode toggle
        this.elements.darkModeToggle.addEventListener('click', () => {
            this.toggleDarkMode();
        });
    }
    
    // File handling methods
    handleFileSelect(files) {
        if (files && files.length > 0) {
            this.loadFile(files[0]);
        }
    }
    
    loadFile(file) {
        const validTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/epub+zip'
        ];

        // Also check file extension for EPUB files (some browsers don't set correct MIME type)
        const fileName = file.name.toLowerCase();
        const isEpub = fileName.endsWith('.epub');

        if (!validTypes.includes(file.type) && !isEpub) {
            this.showStatus('Please select a valid PDF, DOCX, TXT, or EPUB file', 'error');
            return;
        }
        
        this.currentFile = file;
        this.showFilePreview(file);
        this.processFile(file);
    }
    
    showFilePreview(file) {
        // Show file info
        this.elements.fileName.textContent = file.name;
        this.elements.fileSize.textContent = this.formatFileSize(file.size);
        this.elements.fileType.textContent = this.getFileTypeLabel(file.type);
        
        // Show preview section
        this.elements.uploadPreview.style.display = 'block';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'none';
        
        // Show processing status
        this.elements.processingStatus.style.display = 'flex';
    }
    
    async processFile(file) {
        this.showLoading(true, 'Processing document...');
        
        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('file', file);
            
            // Send to backend for processing
            const response = await fetch('/api/process-document', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.extractedText = result.text;
                this.chapters = result.chapters;
                this.showChapterSelection();
                this.showStatus('Document processed successfully', 'success');
            } else {
                throw new Error(result.error || 'Failed to process document');
            }
            
        } catch (error) {
            console.error('Error processing file:', error);
            this.showStatus('Failed to process document. Please try again.', 'error');
        } finally {
            this.showLoading(false);
            this.elements.processingStatus.style.display = 'none';
        }
    }
    
    showChapterSelection() {
        // Show chapter section
        this.elements.chapterSection.style.display = 'block';

        // Calculate pages for each chapter
        this.calculateChapterPages();

        // Populate chapter list
        this.populateChapterList();

        // Update summary stats
        this.updateChapterSummary();

        this.showStatus(`Found ${Object.keys(this.chapters).length} chapters - ready for download`, 'success');
    }

    calculateChapterPages() {
        this.chapterPages = {};

        Object.keys(this.chapters).forEach(chapterTitle => {
            const chapterText = this.chapters[chapterTitle];
            const words = chapterText.split(/\s+/).length;
            const estimatedPages = Math.ceil(words / this.settings.wordsPerPage);

            this.chapterPages[chapterTitle] = {
                wordCount: words,
                pageCount: estimatedPages,
                text: chapterText
            };
        });
    }

    populateChapterList() {
        const chapterList = this.elements.chapterList;
        chapterList.innerHTML = '';

        Object.keys(this.chapters).forEach((chapterTitle, index) => {
            const chapterData = this.chapterPages[chapterTitle];
            const chapterItem = document.createElement('div');
            chapterItem.className = 'chapter-item';
            chapterItem.dataset.chapterTitle = chapterTitle;

            chapterItem.innerHTML = `
                <div class="chapter-item-header">
                    <div class="chapter-item-title">
                        <h4>${chapterTitle}</h4>
                        <p><strong>${chapterData.wordCount.toLocaleString()}</strong> words • <strong>${chapterData.pageCount}</strong> pages</p>
                    </div>
                    <div class="chapter-item-actions">
                        <input type="checkbox" class="chapter-checkbox" data-chapter="${chapterTitle}">
                        <button class="btn btn-success btn-small chapter-download-btn" data-chapter="${chapterTitle}">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <p>${this.chapters[chapterTitle].substring(0, 80)}...</p>
            `;

            // Add click handler for title area only
            const titleArea = chapterItem.querySelector('.chapter-item-title');
            titleArea.addEventListener('click', () => {
                this.selectChapter(chapterTitle);
            });

            // Add download handler
            const downloadBtn = chapterItem.querySelector('.chapter-download-btn');
            downloadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.downloadSingleChapter(chapterTitle);
            });

            // Add checkbox handler
            const checkbox = chapterItem.querySelector('.chapter-checkbox');
            checkbox.addEventListener('change', () => {
                this.updateSelectedChaptersButton();
            });

            chapterList.appendChild(chapterItem);
        });
    }

    filterChapters(searchTerm) {
        const chapterItems = this.elements.chapterList.querySelectorAll('.chapter-item');
        const term = searchTerm.toLowerCase();

        chapterItems.forEach(item => {
            const title = item.dataset.chapterTitle.toLowerCase();
            const text = item.textContent.toLowerCase();

            if (title.includes(term) || text.includes(term)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    selectChapter(chapterTitle) {
        this.selectedChapter = chapterTitle;

        // Update UI
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('selected');
        });
        event.target.closest('.chapter-item').classList.add('selected');

        // Show chapter details
        const chapterData = this.chapterPages[chapterTitle];

        this.elements.selectedChapterTitle.textContent = chapterTitle;
        this.elements.chapterWords.textContent = chapterData.wordCount.toLocaleString();
        this.elements.chapterPages.textContent = chapterData.pageCount;

        // Update page range controls
        this.elements.endPage.max = chapterData.pageCount;
        this.elements.endPage.value = chapterData.pageCount;
        this.elements.startPage.max = chapterData.pageCount;

        // Show chapter details panel
        this.elements.chapterDetails.style.display = 'block';

        // Update preview
        this.updatePagePreview();
    }

    handlePageSelectionChange(selectionType) {
        // Hide all controls first
        this.elements.pageRangeControls.style.display = 'none';
        this.elements.customPagesControls.style.display = 'none';

        // Show relevant controls
        if (selectionType === 'range') {
            this.elements.pageRangeControls.style.display = 'block';
        } else if (selectionType === 'custom') {
            this.elements.customPagesControls.style.display = 'block';
        }

        // Update preview
        this.updatePagePreview();
    }

    updatePagePreview() {
        if (!this.selectedChapter) return;

        const chapterData = this.chapterPages[this.selectedChapter];
        const selectionType = document.querySelector('input[name="pageSelection"]:checked').value;

        let previewText = '';
        let selectedPages = [];

        if (selectionType === 'all') {
            previewText = chapterData.text.substring(0, 500) + '...';
            selectedPages = Array.from({length: chapterData.pageCount}, (_, i) => i + 1);
        } else if (selectionType === 'range') {
            const startPage = parseInt(this.elements.startPage.value) || 1;
            const endPage = parseInt(this.elements.endPage.value) || chapterData.pageCount;

            selectedPages = Array.from({length: endPage - startPage + 1}, (_, i) => startPage + i);
            previewText = this.getPageRangeText(chapterData.text, startPage, endPage, chapterData.pageCount);
        } else if (selectionType === 'custom') {
            const customPagesStr = this.elements.customPages.value;
            selectedPages = this.parseCustomPages(customPagesStr, chapterData.pageCount);
            previewText = this.getCustomPagesText(chapterData.text, selectedPages, chapterData.pageCount);
        }

        this.selectedPages = selectedPages;
        this.elements.chapterText.textContent = previewText;

        // Update button text
        if (selectedPages.length > 0) {
            this.elements.processChapterBtn.textContent = `Process ${selectedPages.length} page(s)`;
            this.elements.processChapterBtn.disabled = false;
        } else {
            this.elements.processChapterBtn.textContent = 'No pages selected';
            this.elements.processChapterBtn.disabled = true;
        }
    }

    parseCustomPages(pagesStr, maxPages) {
        if (!pagesStr.trim()) return [];

        const pages = new Set();
        const parts = pagesStr.split(',');

        parts.forEach(part => {
            part = part.trim();
            if (part.includes('-')) {
                // Range like "5-8"
                const [start, end] = part.split('-').map(n => parseInt(n.trim()));
                if (start && end && start <= end) {
                    for (let i = start; i <= Math.min(end, maxPages); i++) {
                        pages.add(i);
                    }
                }
            } else {
                // Single page
                const pageNum = parseInt(part);
                if (pageNum && pageNum >= 1 && pageNum <= maxPages) {
                    pages.add(pageNum);
                }
            }
        });

        return Array.from(pages).sort((a, b) => a - b);
    }

    getPageRangeText(fullText, startPage, endPage, totalPages) {
        const words = fullText.split(/\s+/);
        const wordsPerPage = this.settings.wordsPerPage;

        const startWordIndex = (startPage - 1) * wordsPerPage;
        const endWordIndex = Math.min(endPage * wordsPerPage, words.length);

        const selectedWords = words.slice(startWordIndex, endWordIndex);
        const text = selectedWords.join(' ');

        return text.substring(0, 500) + (text.length > 500 ? '...' : '');
    }

    getCustomPagesText(fullText, pageNumbers, totalPages) {
        if (pageNumbers.length === 0) return 'No valid pages selected';

        const words = fullText.split(/\s+/);
        const wordsPerPage = this.settings.wordsPerPage;
        let selectedText = '';

        pageNumbers.forEach((pageNum, index) => {
            const startWordIndex = (pageNum - 1) * wordsPerPage;
            const endWordIndex = Math.min(pageNum * wordsPerPage, words.length);

            const pageWords = words.slice(startWordIndex, endWordIndex);
            selectedText += pageWords.join(' ');

            if (index < pageNumbers.length - 1) {
                selectedText += '\n\n--- Page Break ---\n\n';
            }
        });

        return selectedText.substring(0, 500) + (selectedText.length > 500 ? '...' : '');
    }

    processSelectedChapter() {
        if (!this.selectedChapter || !this.selectedPages || this.selectedPages.length === 0) {
            this.showStatus('Please select a chapter and pages first', 'error');
            return;
        }

        // Get the selected content
        const chapterData = this.chapterPages[this.selectedChapter];
        const selectedContent = this.getSelectedContent(chapterData.text, this.selectedPages);

        // Create a temporary chapter with selected content
        this.chapters = {
            [this.selectedChapter]: selectedContent
        };

        // Show grouping section
        this.elements.groupingSection.style.display = 'block';

        // Update status
        this.showStatus(`Selected ${this.selectedPages.length} page(s) from ${this.selectedChapter}`, 'success');

        // Scroll to grouping section
        this.elements.groupingSection.scrollIntoView({ behavior: 'smooth' });
    }

    getSelectedContent(fullText, pageNumbers) {
        const words = fullText.split(/\s+/);
        const wordsPerPage = this.settings.wordsPerPage;
        let selectedText = '';

        pageNumbers.forEach((pageNum, index) => {
            const startWordIndex = (pageNum - 1) * wordsPerPage;
            const endWordIndex = Math.min(pageNum * wordsPerPage, words.length);

            const pageWords = words.slice(startWordIndex, endWordIndex);
            selectedText += pageWords.join(' ');

            if (index < pageNumbers.length - 1) {
                selectedText += '\n\n';
            }
        });

        return selectedText;
    }

    // Chapter download methods
    updateChapterSummary() {
        const chapterCount = Object.keys(this.chapters).length;
        let totalWords = 0;
        let totalPages = 0;

        Object.values(this.chapterPages).forEach(chapter => {
            totalWords += chapter.wordCount;
            totalPages += chapter.pageCount;
        });

        this.elements.totalChapters.textContent = chapterCount;
        this.elements.totalWords.textContent = totalWords.toLocaleString();
        this.elements.totalPages.textContent = totalPages;
    }

    downloadSingleChapter(chapterTitle) {
        const chapterText = this.chapters[chapterTitle];
        const chapterData = this.chapterPages[chapterTitle];

        // Create formatted content
        let content = `${chapterTitle}\n`;
        content += '='.repeat(chapterTitle.length) + '\n\n';
        content += `Word Count: ${chapterData.wordCount.toLocaleString()}\n`;
        content += `Estimated Pages: ${chapterData.pageCount}\n\n`;
        content += chapterText;

        // Download as text file
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.sanitizeFileName(chapterTitle)}.txt`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus(`Downloaded: ${chapterTitle}`, 'success');
    }

    downloadAllChapters() {
        Object.keys(this.chapters).forEach(chapterTitle => {
            setTimeout(() => {
                this.downloadSingleChapter(chapterTitle);
            }, 100); // Small delay to prevent browser blocking
        });

        this.showStatus(`Downloading all ${Object.keys(this.chapters).length} chapters...`, 'success');
    }

    toggleAllChapters(checked) {
        const checkboxes = document.querySelectorAll('.chapter-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateSelectedChaptersButton();
    }

    updateSelectedChaptersButton() {
        const checkboxes = document.querySelectorAll('.chapter-checkbox:checked');
        const count = checkboxes.length;

        this.elements.downloadSelectedChapters.disabled = count === 0;

        if (count === 0) {
            this.elements.downloadSelectedChapters.innerHTML = '<i class="fas fa-download"></i> Download Selected';
        } else {
            this.elements.downloadSelectedChapters.innerHTML = `<i class="fas fa-download"></i> Download ${count} Chapter${count > 1 ? 's' : ''}`;
        }
    }

    downloadSelectedChapters() {
        const checkboxes = document.querySelectorAll('.chapter-checkbox:checked');
        const selectedChapters = Array.from(checkboxes).map(cb => cb.dataset.chapter);

        if (selectedChapters.length === 0) {
            this.showStatus('No chapters selected', 'error');
            return;
        }

        selectedChapters.forEach((chapterTitle, index) => {
            setTimeout(() => {
                this.downloadSingleChapter(chapterTitle);
            }, index * 100); // Stagger downloads
        });

        this.showStatus(`Downloading ${selectedChapters.length} selected chapters...`, 'success');
    }

    createCombinedPDF() {
        if (Object.keys(this.chapters).length === 0) {
            this.showStatus('No chapters to combine', 'error');
            return;
        }

        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;
            const lineHeight = 6;

            Object.keys(this.chapters).forEach((chapterTitle, chapterIndex) => {
                // Add new page for each chapter (except first)
                if (chapterIndex > 0) {
                    doc.addPage();
                    yPosition = 20;
                }

                // Chapter title
                doc.setFontSize(16);
                doc.setFont(undefined, 'bold');
                doc.text(chapterTitle, margin, yPosition);
                yPosition += 15;

                // Chapter stats
                const chapterData = this.chapterPages[chapterTitle];
                doc.setFontSize(10);
                doc.setFont(undefined, 'normal');
                doc.text(`${chapterData.wordCount.toLocaleString()} words • ${chapterData.pageCount} pages`, margin, yPosition);
                yPosition += 15;

                // Chapter content
                doc.setFontSize(10);
                const lines = doc.splitTextToSize(this.chapters[chapterTitle], doc.internal.pageSize.width - 2 * margin);

                lines.forEach(line => {
                    if (yPosition > pageHeight - margin) {
                        doc.addPage();
                        yPosition = margin;
                    }
                    doc.text(line, margin, yPosition);
                    yPosition += lineHeight;
                });
            });

            // Download the PDF
            const fileName = `${this.currentFile ? this.sanitizeFileName(this.currentFile.name) : 'document'}_all_chapters.pdf`;
            doc.save(fileName);

            this.showStatus('Combined PDF created successfully', 'success');

        } catch (error) {
            console.error('Error creating combined PDF:', error);
            this.showStatus('Failed to create combined PDF', 'error');
        }
    }

    sanitizeFileName(fileName) {
        // Remove file extension and sanitize for file system
        return fileName.replace(/\.[^/.]+$/, "").replace(/[^a-z0-9]/gi, '_').toLowerCase();
    }
    
    generatePageGroups() {
        if (!this.selectedChapter) {
            this.showStatus('Please select a chapter first', 'error');
            return;
        }
        
        const chapterText = this.chapters[this.selectedChapter];
        const words = chapterText.split(/\s+/);
        const wordsPerGroup = this.settings.wordsPerPage * this.settings.pagesPerGroup;
        
        this.pageGroups = [];
        
        for (let i = 0; i < words.length; i += wordsPerGroup) {
            const groupWords = words.slice(i, i + wordsPerGroup);
            const groupText = groupWords.join(' ');
            
            const groupNumber = Math.floor(i / wordsPerGroup) + 1;
            const startPage = (groupNumber - 1) * this.settings.pagesPerGroup + 1;
            const endPage = Math.min(startPage + this.settings.pagesPerGroup - 1, 
                                   Math.ceil(words.length / this.settings.wordsPerPage));
            
            this.pageGroups.push({
                groupNumber,
                pages: `${startPage}-${endPage}`,
                text: groupText,
                wordCount: groupWords.length
            });
        }
        
        this.showPageGroups();
        this.elements.exportSection.style.display = 'block';
        this.showPreview();
    }
    
    showPageGroups() {
        const pageGroupsContainer = this.elements.pageGroups;
        pageGroupsContainer.innerHTML = '';
        
        this.pageGroups.forEach(group => {
            const groupElement = document.createElement('div');
            groupElement.className = 'page-group';
            groupElement.innerHTML = `
                <div class="page-group-header">
                    <h4>Group ${group.groupNumber} (Pages ${group.pages})</h4>
                    <div class="page-group-stats">
                        <span><i class="fas fa-align-left"></i> ${group.wordCount} words</span>
                    </div>
                </div>
                <div class="page-group-content">
                    ${group.text.substring(0, 200)}...
                </div>
            `;
            
            pageGroupsContainer.appendChild(groupElement);
        });
        
        pageGroupsContainer.style.display = 'block';
    }
    
    showPreview() {
        this.currentGroupIndex = 0;
        this.updatePreview();
        this.updateNavigationButtons();
    }
    
    updatePreview() {
        if (this.pageGroups.length === 0) return;
        
        const group = this.pageGroups[this.currentGroupIndex];
        let content = '';
        
        if (this.settings.includeHeaders) {
            content += `=== ${this.selectedChapter} ===\n`;
            content += `Group ${group.groupNumber} (Pages ${group.pages})\n\n`;
        }
        
        content += group.text;
        
        if (this.settings.includeNumbers) {
            content += `\n\n--- Page Group ${group.groupNumber} ---`;
        }
        
        this.elements.previewContent.textContent = content;
        this.elements.groupIndicator.textContent = `Group ${this.currentGroupIndex + 1} of ${this.pageGroups.length}`;
    }
    
    navigateGroup(direction) {
        this.currentGroupIndex += direction;
        this.currentGroupIndex = Math.max(0, Math.min(this.currentGroupIndex, this.pageGroups.length - 1));
        this.updatePreview();
        this.updateNavigationButtons();
    }
    
    updateNavigationButtons() {
        this.elements.prevGroup.disabled = this.currentGroupIndex === 0;
        this.elements.nextGroup.disabled = this.currentGroupIndex === this.pageGroups.length - 1;
    }

    // Export methods
    exportAsPDF() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;
            const lineHeight = 6;

            this.pageGroups.forEach((group, index) => {
                // Add new page if not the first group
                if (index > 0) {
                    doc.addPage();
                    yPosition = 20;
                }

                // Add header if enabled
                if (this.settings.includeHeaders) {
                    doc.setFontSize(16);
                    doc.setFont(undefined, 'bold');
                    doc.text(`${this.selectedChapter}`, margin, yPosition);
                    yPosition += 10;

                    doc.setFontSize(12);
                    doc.setFont(undefined, 'normal');
                    doc.text(`Group ${group.groupNumber} (Pages ${group.pages})`, margin, yPosition);
                    yPosition += 15;
                }

                // Add content
                doc.setFontSize(10);
                const lines = doc.splitTextToSize(group.text, doc.internal.pageSize.width - 2 * margin);

                lines.forEach(line => {
                    if (yPosition > pageHeight - margin) {
                        doc.addPage();
                        yPosition = margin;
                    }
                    doc.text(line, margin, yPosition);
                    yPosition += lineHeight;
                });

                // Add page numbers if enabled
                if (this.settings.includeNumbers) {
                    yPosition += 10;
                    doc.setFontSize(8);
                    doc.text(`--- Page Group ${group.groupNumber} ---`, margin, yPosition);
                }
            });

            // Download the PDF
            const fileName = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.pdf`;
            doc.save(fileName);

            this.showStatus('PDF exported successfully', 'success');

        } catch (error) {
            console.error('Error exporting PDF:', error);
            this.showStatus('Failed to export PDF', 'error');
        }
    }

    exportAsText() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        let content = '';

        this.pageGroups.forEach(group => {
            if (this.settings.includeHeaders) {
                content += `=== ${this.selectedChapter} ===\n`;
                content += `Group ${group.groupNumber} (Pages ${group.pages})\n\n`;
            }

            content += group.text;

            if (this.settings.includeNumbers) {
                content += `\n\n--- Page Group ${group.groupNumber} ---\n\n`;
            } else {
                content += '\n\n';
            }

            content += '\n' + '='.repeat(50) + '\n\n';
        });

        // Download as text file
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.txt`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus('Text file exported successfully', 'success');
    }

    exportAsJSON() {
        if (this.pageGroups.length === 0) {
            this.showStatus('No page groups to export', 'error');
            return;
        }

        const exportData = {
            document: this.currentFile ? this.currentFile.name : 'Unknown',
            chapter: this.selectedChapter,
            settings: this.settings,
            groups: this.pageGroups.map(group => ({
                groupNumber: group.groupNumber,
                pages: group.pages,
                wordCount: group.wordCount,
                text: group.text
            })),
            exportDate: new Date().toISOString()
        };

        // Download as JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.selectedChapter.replace(/[^a-z0-9]/gi, '_')}_grouped.json`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus('JSON file exported successfully', 'success');
    }

    // Utility methods
    removeFile() {
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.selectedChapter = null;
        this.selectedPages = null;
        this.pageGroups = [];
        this.chapterPages = {};

        // Reset UI
        this.elements.uploadPreview.style.display = 'none';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'block';
        this.elements.chapterSection.style.display = 'none';
        this.elements.chapterDetails.style.display = 'none';
        this.elements.groupingSection.style.display = 'none';
        this.elements.exportSection.style.display = 'none';

        // Reset search and selections
        this.elements.chapterSearch.value = '';
        this.elements.selectAllChapters.checked = false;
        this.updateSelectedChaptersButton();

        this.showStatus('File removed', 'info');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileTypeLabel(mimeType) {
        const types = {
            'application/pdf': 'PDF Document',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
            'text/plain': 'Text File',
            'application/epub+zip': 'EPUB E-book'
        };
        return types[mimeType] || 'Unknown';
    }

    showStatus(message, type = 'info') {
        this.elements.statusMessage.textContent = message;
        this.elements.statusMessage.className = `status-message ${type}`;

        // Auto-clear after 3 seconds
        setTimeout(() => {
            this.elements.statusMessage.textContent = '';
            this.elements.statusMessage.className = 'status-message';
        }, 3000);
    }

    showLoading(show, text = 'Processing...') {
        this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        if (text) {
            this.elements.loadingText.textContent = text;
        }
    }

    toggleDarkMode() {
        const body = document.body;
        const isDark = body.getAttribute('data-theme') === 'dark';

        if (isDark) {
            body.removeAttribute('data-theme');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            body.setAttribute('data-theme', 'dark');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentProcessor();
});
