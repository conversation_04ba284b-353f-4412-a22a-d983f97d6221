/**
 * Document Processor - Chapter & Page Grouping Tool
 * Complete web application for processing documents and grouping content
 */

class DocumentProcessor {
    constructor() {
        // Core properties
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.selectedChapter = null;
        this.selectedPages = null;
        this.pageGroups = [];
        this.currentGroupIndex = 0;
        this.chapterPages = {};
        
        // Settings
        this.settings = {
            pagesPerGroup: 2,
            wordsPerPage: 300,
            includeHeaders: true,
            includeNumbers: true
        };
        
        // DOM elements
        this.elements = {};
        
        this.init();
    }
    
    init() {
        try {
            this.setupElements();
            this.setupEventListeners();
            console.log('DocumentProcessor initialized successfully');
        } catch (error) {
            console.error('Error initializing DocumentProcessor:', error);
        }
    }
    
    setupElements() {
        // Get all required DOM elements
        const elementIds = [
            'uploadArea', 'fileInput', 'uploadPreview', 'fileName', 'fileSize', 'fileType',
            'removeFile', 'processingStatus', 'chapterSection', 'chapterList',
            'downloadAllChapters', 'totalChapters', 'statusMessage', 'loadingOverlay',
            'loadingText', 'darkModeToggle'
        ];
        
        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
            if (!this.elements[id]) {
                console.error(`Element not found: ${id}`);
            }
        });
    }
    
    setupEventListeners() {
        // File upload
        this.elements.uploadArea.addEventListener('click', () => {
            this.elements.fileInput.click();
        });
        
        this.elements.fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });
        
        // Drag and drop
        this.elements.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.add('dragover');
        });
        
        this.elements.uploadArea.addEventListener('dragleave', () => {
            this.elements.uploadArea.classList.remove('dragover');
        });
        
        this.elements.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });
        
        // Remove file
        this.elements.removeFile.addEventListener('click', () => {
            this.removeFile();
        });
        
        // Settings
        this.elements.pagesPerGroup.addEventListener('input', (e) => {
            this.settings.pagesPerGroup = parseInt(e.target.value);
        });
        
        this.elements.wordsPerPage.addEventListener('input', (e) => {
            this.settings.wordsPerPage = parseInt(e.target.value);
        });
        
        this.elements.includeHeaders.addEventListener('change', (e) => {
            this.settings.includeHeaders = e.target.checked;
        });
        
        this.elements.includeNumbers.addEventListener('change', (e) => {
            this.settings.includeNumbers = e.target.checked;
        });
        
        // Chapter download button
        this.elements.downloadAllChapters.addEventListener('click', () => {
            this.downloadAllChaptersAsZip();
        });

        // Action buttons
        this.elements.generateGroups.addEventListener('click', () => {
            this.generatePageGroups();
        });
        
        // Export buttons
        this.elements.exportPDF.addEventListener('click', () => {
            this.exportAsPDF();
        });
        
        this.elements.exportTXT.addEventListener('click', () => {
            this.exportAsText();
        });
        
        this.elements.exportJSON.addEventListener('click', () => {
            this.exportAsJSON();
        });
        
        // Navigation
        this.elements.prevGroup.addEventListener('click', () => {
            this.navigateGroup(-1);
        });
        
        this.elements.nextGroup.addEventListener('click', () => {
            this.navigateGroup(1);
        });
        
        // Dark mode toggle
        this.elements.darkModeToggle.addEventListener('click', () => {
            this.toggleDarkMode();
        });
    }
    
    // File handling methods
    handleFileSelect(files) {
        if (files && files.length > 0) {
            this.loadFile(files[0]);
        }
    }
    
    loadFile(file) {
        const validTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/epub+zip'
        ];

        // Also check file extension for EPUB files (some browsers don't set correct MIME type)
        const fileName = file.name.toLowerCase();
        const isEpub = fileName.endsWith('.epub');

        if (!validTypes.includes(file.type) && !isEpub) {
            this.showStatus('Please select a valid PDF, DOCX, TXT, or EPUB file', 'error');
            return;
        }
        
        this.currentFile = file;
        this.showFilePreview(file);
        this.processFile(file);
    }
    
    showFilePreview(file) {
        // Show file info
        this.elements.fileName.textContent = file.name;
        this.elements.fileSize.textContent = this.formatFileSize(file.size);
        this.elements.fileType.textContent = this.getFileTypeLabel(file.type);
        
        // Show preview section
        this.elements.uploadPreview.style.display = 'block';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'none';
        
        // Show processing status
        this.elements.processingStatus.style.display = 'flex';
    }
    
    async processFile(file) {
        this.showLoading(true, 'Processing document...');
        
        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('file', file);
            
            // Send to backend for processing
            const response = await fetch('/api/process-document', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.extractedText = result.text;
                this.chapters = result.chapters;
                this.showChapterSelection();
                this.showStatus('Document processed successfully', 'success');
            } else {
                throw new Error(result.error || 'Failed to process document');
            }
            
        } catch (error) {
            console.error('Error processing file:', error);
            this.showStatus('Failed to process document. Please try again.', 'error');
        } finally {
            this.showLoading(false);
            this.elements.processingStatus.style.display = 'none';
        }
    }
    
    showChapterSelection() {
        // Show chapter section
        this.elements.chapterSection.style.display = 'block';

        // Calculate pages for each chapter
        this.calculateChapterPages();

        // Populate chapter list
        this.populateChapterList();

        // Update summary stats
        this.elements.totalChapters.textContent = Object.keys(this.chapters).length;

        this.showStatus(`Found ${Object.keys(this.chapters).length} chapters - ready for download`, 'success');
    }

    calculateChapterPages() {
        this.chapterPages = {};

        Object.keys(this.chapters).forEach(chapterTitle => {
            const chapterText = this.chapters[chapterTitle];
            const words = chapterText.split(/\s+/).length;
            const estimatedPages = Math.ceil(words / this.settings.wordsPerPage);

            this.chapterPages[chapterTitle] = {
                wordCount: words,
                pageCount: estimatedPages,
                text: chapterText
            };
        });
    }

    populateChapterList() {
        const chapterList = this.elements.chapterList;
        chapterList.innerHTML = '';

        Object.keys(this.chapters).forEach((chapterTitle, index) => {
            const chapterData = this.chapterPages[chapterTitle];
            const chapterItem = document.createElement('div');
            chapterItem.className = 'chapter-item';

            chapterItem.innerHTML = `
                <div class="chapter-item-header">
                    <h3>${chapterTitle}</h3>
                    <button class="btn btn-success chapter-download-btn" data-chapter="${chapterTitle}">
                        <i class="fas fa-download"></i> Download Chapter
                    </button>
                </div>
                <div class="chapter-stats">
                    <strong>${chapterData.wordCount.toLocaleString()}</strong> words • <strong>${chapterData.pageCount}</strong> pages
                </div>
                <div class="chapter-preview">
                    ${this.chapters[chapterTitle].substring(0, 150)}...
                </div>
            `;

            // Add download handler
            const downloadBtn = chapterItem.querySelector('.chapter-download-btn');
            downloadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.downloadSingleChapter(chapterTitle);
            });

            chapterList.appendChild(chapterItem);
        });
    }



    // Chapter download methods
    updateChapterSummary() {
        const chapterCount = Object.keys(this.chapters).length;
        let totalWords = 0;
        let totalPages = 0;

        Object.values(this.chapterPages).forEach(chapter => {
            totalWords += chapter.wordCount;
            totalPages += chapter.pageCount;
        });

        this.elements.totalChapters.textContent = chapterCount;
        this.elements.totalWords.textContent = totalWords.toLocaleString();
        this.elements.totalPages.textContent = totalPages;
    }

    downloadSingleChapter(chapterTitle) {
        const chapterText = this.chapters[chapterTitle];
        const chapterData = this.chapterPages[chapterTitle];

        // Create formatted content
        let content = `${chapterTitle}\n`;
        content += '='.repeat(chapterTitle.length) + '\n\n';
        content += `Word Count: ${chapterData.wordCount.toLocaleString()}\n`;
        content += `Estimated Pages: ${chapterData.pageCount}\n\n`;
        content += chapterText;

        // Download as text file
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.sanitizeFileName(chapterTitle)}.txt`;
        link.click();
        URL.revokeObjectURL(url);

        this.showStatus(`Downloaded: ${chapterTitle}`, 'success');
    }

    async downloadAllChaptersAsZip() {
        if (Object.keys(this.chapters).length === 0) {
            this.showStatus('No chapters to download', 'error');
            return;
        }

        this.showLoading(true, 'Creating ZIP file with all chapters...');

        try {
            const zip = new JSZip();
            const chaptersFolder = zip.folder("chapters");

            // Add each chapter as a text file
            Object.keys(this.chapters).forEach((chapterTitle, index) => {
                const chapterText = this.chapters[chapterTitle];
                const chapterData = this.chapterPages[chapterTitle];

                // Create formatted content
                let content = `${chapterTitle}\n`;
                content += '='.repeat(chapterTitle.length) + '\n\n';
                content += `Word Count: ${chapterData.wordCount.toLocaleString()}\n`;
                content += `Estimated Pages: ${chapterData.pageCount}\n\n`;
                content += chapterText;

                // Add to ZIP with chapter number prefix for ordering
                const fileName = `${String(index + 1).padStart(2, '0')}_${this.sanitizeFileName(chapterTitle)}.txt`;
                chaptersFolder.file(fileName, content);
            });

            // Create table of contents
            let tocContent = "TABLE OF CONTENTS\n";
            tocContent += "=".repeat(17) + "\n\n";
            Object.keys(this.chapters).forEach((chapterTitle, index) => {
                const chapterData = this.chapterPages[chapterTitle];
                tocContent += `${index + 1}. ${chapterTitle}\n`;
                tocContent += `   ${chapterData.wordCount.toLocaleString()} words • ${chapterData.pageCount} pages\n\n`;
            });

            zip.file("00_Table_of_Contents.txt", tocContent);

            // Generate ZIP file
            const zipBlob = await zip.generateAsync({type: "blob"});

            // Download ZIP file
            const url = URL.createObjectURL(zipBlob);
            const link = document.createElement('a');
            link.href = url;
            const fileName = this.currentFile ? this.sanitizeFileName(this.currentFile.name) : 'document';
            link.download = `${fileName}_chapters.zip`;
            link.click();
            URL.revokeObjectURL(url);

            this.showStatus(`Downloaded ZIP with ${Object.keys(this.chapters).length} chapters`, 'success');

        } catch (error) {
            console.error('Error creating ZIP file:', error);
            this.showStatus('Failed to create ZIP file', 'error');
        } finally {
            this.showLoading(false);
        }
    }



    sanitizeFileName(fileName) {
        // Remove file extension and sanitize for file system
        return fileName.replace(/\.[^/.]+$/, "").replace(/[^a-z0-9]/gi, '_').toLowerCase();
    }
    




    // Utility methods
    removeFile() {
        this.currentFile = null;
        this.extractedText = '';
        this.chapters = {};
        this.chapterPages = {};

        // Reset UI
        this.elements.uploadPreview.style.display = 'none';
        this.elements.uploadArea.querySelector('.upload-content').style.display = 'block';
        this.elements.chapterSection.style.display = 'none';

        this.showStatus('File removed', 'info');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileTypeLabel(mimeType) {
        const types = {
            'application/pdf': 'PDF Document',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
            'text/plain': 'Text File',
            'application/epub+zip': 'EPUB E-book'
        };
        return types[mimeType] || 'Unknown';
    }

    showStatus(message, type = 'info') {
        this.elements.statusMessage.textContent = message;
        this.elements.statusMessage.className = `status-message ${type}`;

        // Auto-clear after 3 seconds
        setTimeout(() => {
            this.elements.statusMessage.textContent = '';
            this.elements.statusMessage.className = 'status-message';
        }, 3000);
    }

    showLoading(show, text = 'Processing...') {
        this.elements.loadingOverlay.style.display = show ? 'flex' : 'none';
        if (text) {
            this.elements.loadingText.textContent = text;
        }
    }

    toggleDarkMode() {
        const body = document.body;
        const isDark = body.getAttribute('data-theme') === 'dark';

        if (isDark) {
            body.removeAttribute('data-theme');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            body.setAttribute('data-theme', 'dark');
            this.elements.darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentProcessor();
});
