#!/bin/bash

# Document Chapter Extractor - Startup Script

echo "🚀 Starting Document Chapter Extractor..."
echo "📋 Building application..."

# Build the application
mvn clean install

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🌐 Starting server..."
    echo "📍 Application will be available at: http://localhost:8080"
    echo "⏹️  Press Ctrl+C to stop the server"
    echo ""
    
    # Run the application
    mvn spring-boot:run
else
    echo "❌ Build failed! Please check the error messages above."
    exit 1
fi
