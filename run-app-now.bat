@echo off
echo.
echo ========================================
echo   Document Chapter Extractor Launcher
echo ========================================
echo.

echo 🔍 Checking for Java installation...

REM Check if java is available in PATH
java -version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Java found in PATH
    goto :maven_check
)

REM Check common Java installation locations
set JAVA_LOCATIONS[0]="C:\Program Files\Java\jdk*\bin\java.exe"
set JAVA_LOCATIONS[1]="C:\Program Files\Eclipse Adoptium\jdk*\bin\java.exe"
set JAVA_LOCATIONS[2]="C:\Program Files\OpenJDK\jdk*\bin\java.exe"
set JAVA_LOCATIONS[3]="C:\Users\<USER>\java17\*\bin\java.exe"

for /L %%i in (0,1,3) do (
    for /f "delims=" %%j in ('dir /b /s "!JAVA_LOCATIONS[%%i]!" 2^>nul ^| findstr /v "jre"') do (
        set FOUND_JAVA=%%j
        goto :found_java
    )
)

echo ❌ Java not found. Please install Java 17:
echo.
echo 💡 Quick Installation:
echo    1. Go to: https://adoptium.net/
echo    2. Download Java 17 ^(LTS^)
echo    3. Install and restart this script
echo.
echo 🔗 Direct link: https://adoptium.net/temurin/releases/?version=17
echo.
pause
exit /b 1

:found_java
echo ✅ Java found at: %FOUND_JAVA%
set JAVA_HOME=%FOUND_JAVA:~0,-13%
set PATH=%JAVA_HOME%\bin;%PATH%

:maven_check
echo 🔍 Checking Maven installation...

REM Check if our installed Maven works
set MAVEN_HOME=C:\Users\<USER>\maven
set PATH=%MAVEN_HOME%\mvn\bin;%PATH%

"%MAVEN_HOME%\mvn\bin\mvn.cmd" --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Maven not working properly
    echo 💡 Please run setup-maven-simple.bat first
    pause
    exit /b 1
)

echo ✅ Maven is ready

echo.
echo 🚀 Starting Document Chapter Extractor...
echo.

REM Navigate to project directory
cd document-chapter-extractor

echo 🔨 Building application (first time may take a few minutes)...
"%MAVEN_HOME%\mvn\bin\mvn.cmd" clean install -q

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Build failed
    echo.
    echo 💡 Try running with verbose output:
    echo    mvn clean install
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🌐 Starting server...
echo 📍 Application will be available at: http://localhost:8080
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start the application
"%MAVEN_HOME%\mvn\bin\mvn.cmd" spring-boot:run
